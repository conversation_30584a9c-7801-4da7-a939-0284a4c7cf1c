server:
  port: 8527

spring:
  application:
    name: high-availability-control-platform
  messages:
    basename: i18n/messages
  session:
    #登录有效期设置
    timeout: 900
    store-type: hazelcast
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher
    hiddenmethod:
      filter:
        enabled: true
    throw-exception-if-no-handler-found: true
  jackson:
    serialization:
      write_dates_as_timestamps: false
  task:
    scheduling:
      pool:
        size: 32
#  main:
#    allow-bean-definition-overriding: true

lemon:
  alerting:
    resolver: messageResource
    prefix: HAC
  # session 配置信息
  session:
    session-id:
      strategy: Header
      headerName: token
  # 多数据源配置信息
  dataSources:
    primary:
      type: com.alibaba.druid.pool.DruidDataSource
      driverClassName: oracle.jdbc.OracleDriver
      url: ***************************************
      username: payadm
      password: '******'
  dynamicDataSource:
    enabled: true
    defaultDataSource: primary
  # id生成器配置信息
  idgen:
    generator: simple
  cache:
    jcache:
      spring:
        cache:
          jcache:
            config: config/ehcache3.xml
            provider: org.ehcache.jsr107.EhcacheCachingProvider
  sql:
    # print sql set 'DEBUG'
    level: DEBUG
  security:
    principal:
      principalNameExpression: username
    #认证地址
    authentication:
      loginPathPrefix: /v1/sys/login              #默认 /security/login
#      refreshPath: /v1/sys/refresh                #默认 /security/refresh 认证续约需要依赖session redis,此项目不依赖redis，所以不使用
      logoutPath: /v1/sys/logout                  #默认 /security/logout
    authorizeRequests:
      #配置不进行认证检查的交易请求url
      permitAll:
        - /v1/sys/captcha/getCaptcha
        - /v1/sys/cipher/rsa/publickey
        - /v1/sys/cipher/sm2/publickey
        - /v1/sys/cipher/keys
        - /v1/tenant/sys/log/add
        - /v1/tenant/workspace/application/sync
        - /v1/tenant/workspace/application/sync-all
        - /v1/scim/sync
  actuator:
    health:
      redis:
        enabled: false

mybatis:
  mapper-locations:
    - classpath*:mapper/*.xml

hacp:
  web:
    admin:
      scim-iam:
        server:
          #单点登录服务器
          address: http://auth.4a.test:8002
          connect-timeout: 15000
          read-timeout: 15000
        #应用ID
        app-id: bfc0cce4-df98-4e44-b8a2-97ab5f3d2988
        #应用secret
        app-secret: 00902a86-880c-4494-af66-983e6f41c617
        enabled-sync-user-organization: true
        enabled-organization: true
        # 启用同步任务
        enabled: true
        # 单点登录验证回调地址参数
        service: http://*************
        sso-server: http://auth.4a.test:8002/auth/cas/p3/serviceValidate
      cache-type: hazelcast
      log:
        management: true
      sensitive:
        base-packages:
          - com.cmpay.hacp.tenant.dao.ITenantDao
          - com.cmpay.hacp.tenant.dao.ITenantExtDao
          - com.cmpay.hacp.tenant.dao.IWorkspaceExtDao
          - com.cmpay.hacp.tenant.dao.ITenantWorkspaceUserExtDao

    dashboard:
      grafana:
        dashboard-options:
          theme: light
          refresh: 1m
          kiosk: true

  push:
    watch:
      enable: false # 为true打开流量调度定时任务，使用流量调度时必须打开！

  management:
    discovery:
      enabled: false
    tenant-session-filter:
      permitUrls:
        - /v1/tenant/**
        - /v1/sys/**
        - /v1/scim/sync
        - /v1/message/notice/**
  emergence:
    cmft:
      dispatch:
        url: http://10.176.243.130:9000/strategy-admin-center-api/v3/openapi
camunda:
  bpm:
    authorization:
      enabled: false
      tenant-check-enabled: false
    admin-user:
      id: admin  #用户名
      password: 123456  #密码
      firstName: yu
    filter:
      create: All tasks
#logging configuration
logging:
  config: classpath:config/logback-spring.xml

hazelcast:
  cluster-name: hzcluster-hacp

user-ssh-path: ~/.ssh/id_rsa

cmft:
  dispatch:
    strategy:
      query:
        type: 1
management:
  server:
    port: 9527

upms:
  user-sync-config:
    enabled: true