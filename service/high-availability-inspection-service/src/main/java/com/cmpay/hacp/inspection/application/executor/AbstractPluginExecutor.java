package com.cmpay.hacp.inspection.application.executor;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.cmpay.hacp.inspection.application.service.RuleMatchingService;
import com.cmpay.hacp.inspection.domain.model.plugin.InspectionResult;
import com.cmpay.hacp.inspection.domain.model.rule.RuleMatchingResult;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.PluginScriptDO;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.PluginScriptParameterDO;
import com.cmpay.hacp.inspection.infrastructure.repository.PluginScriptParameterRepository;
import com.cmpay.hacp.inspection.infrastructure.repository.PluginScriptRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@RequiredArgsConstructor
public abstract class AbstractPluginExecutor implements PluginExecutor {
    protected final RemoteExecutionService remoteExecutionService;
    protected final PluginScriptParameterRepository pluginScriptParameterRepository;
    protected final PluginScriptRepository pluginScriptRepository;
    protected final RuleMatchingService ruleMatchingService;


    @Override
    public InspectionResult execute(String ruleId, String pluginId, TargetHost targetHost) {
        log.info("Executing inspection script, Rule ID: {}, Plugin ID: {}, Target: {}", ruleId, pluginId, targetHost.getHost());

        PluginScriptDO pluginScriptDO = pluginScriptRepository.getOne(
                Wrappers.lambdaQuery(PluginScriptDO.class)
                        .eq(PluginScriptDO::getPluginId, pluginId));

        if (pluginScriptDO == null) {
            log.error("Plugin script not found for pluginId: {}", pluginId);
            return InspectionResult.builder()
                    .success(false)
                    .scriptExecutionSuccess(false)
                    .ruleMatchingSuccess(false)
                    .message("Plugin script not found")
                    .details("Plugin script not found for pluginId: " + pluginId)
                    .build();
        }

        String scriptContent = pluginScriptDO.getScriptContent();

        // 获取param_name到param_value的映射
        Map<String, Object> objectMap = pluginScriptParameterRepository.getMap(
                Wrappers.lambdaQuery(PluginScriptParameterDO.class)
                        .select(PluginScriptParameterDO::getParamName, PluginScriptParameterDO::getParamValue)
                        .eq(PluginScriptParameterDO::getPluginId, pluginId));

        // 转换为Map<String, String>
        Map<String, String> paramMap = objectMap.entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> String.valueOf(entry.getValue())
                ));

        // 替换脚本中的参数
        for (Map.Entry<String, String> entry : paramMap.entrySet()) {
            scriptContent = scriptContent.replace("${" + entry.getKey() + "}", entry.getValue());
        }

        // 执行远程脚本
        RemoteExecutionResult result = remoteExecutionService.executeScript(
                SshConnectionConfig.builder()
                        .host(targetHost.getHostName())
                        .port(targetHost.getPort())
                        .username(targetHost.getUsername())
                        .password(targetHost.getPassword())
                        .privateKeyPath(targetHost.getPrivateKeyPath())
                        .privateKeyContent(targetHost.getPrivateKeyContent())
                        .build(),
                ScriptExecutionRequest.builder()
                        .scriptContent(scriptContent)
                        .scriptType(ScriptExecutionRequest.ScriptType.SHELL)
                        .build()
        );

        boolean scriptExecutionSuccess = result.isSuccess();
        log.debug("Script execution completed: success={}, exitCode={}", scriptExecutionSuccess, result.getExitCode());

        // 巡检规则匹配
        RuleMatchingResult ruleMatchingResult = null;
        boolean ruleMatchingSuccess = false;

        if (scriptExecutionSuccess && StringUtils.hasText(result.getStdout())) {
            try {
                ruleMatchingResult = ruleMatchingService.matchRule(ruleId, pluginId, result.getStdout());
                ruleMatchingSuccess = ruleMatchingResult.isSuccess();
                log.debug("Rule matching completed: success={}, message={}", ruleMatchingSuccess, ruleMatchingResult.getMessage());
            } catch (Exception e) {
                log.error("Error during rule matching for ruleId: {}, pluginId: {}", ruleId, pluginId, e);
                ruleMatchingResult = RuleMatchingResult.builder()
                        .success(false)
                        .ruleId(ruleId)
                        .pluginId(pluginId)
                        .errorMessage("Rule matching error: " + e.getMessage())
                        .build();
            }
        } else {
            log.warn("Skipping rule matching due to script execution failure or empty output");
        }

        // 构建最终结果
        boolean overallSuccess = scriptExecutionSuccess && ruleMatchingSuccess;
        String message = buildResultMessage(scriptExecutionSuccess, ruleMatchingSuccess, ruleMatchingResult);
        String details = buildResultDetails(result, ruleMatchingResult);

        return InspectionResult.builder()
                .success(overallSuccess)
                .scriptExecutionSuccess(scriptExecutionSuccess)
                .ruleMatchingSuccess(ruleMatchingSuccess)
                .message(message)
                .details(details)
                .ruleMatchingResult(ruleMatchingResult)
                .build();
    }

    /**
     * 构建结果消息
     */
    private String buildResultMessage(boolean scriptExecutionSuccess, boolean ruleMatchingSuccess, RuleMatchingResult ruleMatchingResult) {
        if (!scriptExecutionSuccess) {
            return "脚本执行失败";
        }

        if (!ruleMatchingSuccess) {
            if (ruleMatchingResult != null && StringUtils.hasText(ruleMatchingResult.getMessage())) {
                return "规则匹配失败: " + ruleMatchingResult.getMessage();
            }
            return "规则匹配失败";
        }

        return "巡检通过";
    }

    /**
     * 构建结果详情
     */
    private String buildResultDetails(RemoteExecutionResult result, RuleMatchingResult ruleMatchingResult) {
        StringBuilder details = new StringBuilder();

        // 脚本执行结果
        details.append("=== 脚本执行结果 ===\n");
        details.append("执行状态: ").append(result.isSuccess() ? "成功" : "失败").append("\n");
        details.append("退出码: ").append(result.getExitCode()).append("\n");
        details.append("执行时间: ").append(result.getExecutionTime()).append("ms\n");

        if (StringUtils.hasText(result.getStdout())) {
            details.append("标准输出:\n").append(result.getStdout()).append("\n");
        }

        if (StringUtils.hasText(result.getStderr())) {
            details.append("错误输出:\n").append(result.getStderr()).append("\n");
        }

        // 规则匹配结果
        if (ruleMatchingResult != null) {
            details.append("\n=== 规则匹配结果 ===\n");
            details.append("匹配状态: ").append(ruleMatchingResult.isSuccess() ? "通过" : "失败").append("\n");

            if (StringUtils.hasText(ruleMatchingResult.getMessage())) {
                details.append("匹配信息: ").append(ruleMatchingResult.getMessage()).append("\n");
            }

            if (StringUtils.hasText(ruleMatchingResult.getErrorMessage())) {
                details.append("错误信息: ").append(ruleMatchingResult.getErrorMessage()).append("\n");
            }

            if (StringUtils.hasText(ruleMatchingResult.getSuggestion())) {
                details.append("治理建议: ").append(ruleMatchingResult.getSuggestion()).append("\n");
            }
        }

        return details.toString();
    }
}
