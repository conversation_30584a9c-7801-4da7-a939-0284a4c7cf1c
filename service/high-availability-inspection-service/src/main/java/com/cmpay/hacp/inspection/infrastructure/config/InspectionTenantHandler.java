package com.cmpay.hacp.inspection.infrastructure.config;

import com.baomidou.mybatisplus.extension.plugins.handler.TenantLineHandler;
import com.cmpay.hacp.tenant.utils.TenantUtils;
import net.sf.jsqlparser.expression.Expression;
import net.sf.jsqlparser.expression.StringValue;
import org.springframework.stereotype.Component;

@Component
public class InspectionTenantHandler implements TenantLineHandler {
    @Override
    public Expression getTenantId() {
        return new StringValue(TenantUtils.getWorkspaceId());
    }
    @Override
    public String getTenantIdColumn() {
        return "workspace_id";
    }
}
