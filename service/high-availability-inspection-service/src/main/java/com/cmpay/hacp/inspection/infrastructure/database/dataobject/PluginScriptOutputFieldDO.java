package com.cmpay.hacp.inspection.infrastructure.database.dataobject;

import com.baomidou.mybatisplus.annotation.TableName;
import com.cmpay.hacp.inspection.domain.model.enums.ScriptResultFieldType;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 巡检插件脚本结构化输出字段定义
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("inspection_plugin_script_output_filed")
public class PluginScriptOutputFieldDO extends BaseDO {
    /**
     * 插件ID
     */
    private String pluginId;

    /**
     * 字段名称(如cpu.usage)
     */
    private String fieldName;

    /**
     * 示例值
     */
    private String exampleValue;

    /**
     * 单位(如%)
     */
    private String fieldUnit;

    /**
     * 字段类型(0:数值型, 1:字符串型, 2:布尔型)
     */
    private ScriptResultFieldType fieldType;

    /**
     * 描述
     */
    private String description;
}
