package com.cmpay.hacp.inspection.application.executor;

import com.cmpay.hacp.inspection.application.service.RuleMatchingService;
import com.cmpay.hacp.inspection.domain.model.enums.PluginType;
import com.cmpay.hacp.inspection.infrastructure.repository.PluginScriptParameterRepository;
import com.cmpay.hacp.inspection.infrastructure.repository.PluginScriptRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class ShellPluginExecutor extends AbstractPluginExecutor {

    public ShellPluginExecutor(RemoteExecutionService remoteExecutionService,
                               PluginScriptParameterRepository pluginScriptParameterRepository,
                               PluginScriptRepository pluginScriptRepository,
                               RuleMatchingService ruleMatchingService) {
        super(remoteExecutionService, pluginScriptParameterRepository, pluginScriptRepository, ruleMatchingService);
    }

    @Override
    public String getName() {
        return "Shell Script Inspection";
    }

    @Override
    public boolean supports(PluginType delimiter) {
        return delimiter == PluginType.SHELL_SCRIPT;
    }
}
