package com.cmpay.hacp.inspection.domain.model.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

@Getter
public enum RuleAngleViewEnum {

    CORE_SERVICE(1, "中枢产品(基础设施服务)"),
    BUSINESS_SERVICE(2, "业务应用(业务功能服务)"),
    MIDDLEWARE(3, "中间件(支撑服务)");

    @JsonValue
    private final Integer code;
    private final String desc;

    private static final Map<Integer, RuleAngleViewEnum> CODE_MAP = Arrays.stream(values())
            .collect(Collectors.toMap(RuleAngleViewEnum::getCode, type -> type));


    RuleAngleViewEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static RuleAngleViewEnum getByCode(Integer code) {
        return code == null ? null : CODE_MAP.get(code);
    }

}

