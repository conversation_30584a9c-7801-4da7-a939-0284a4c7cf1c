ext {
    versions = [
            springBoot: '2.6.15', // 框架当前使用的版本
            mapstruct: '1.6.3',
            mybatisPlus: '3.5.12'
    ]
}

dependencies {
    implementation project(':interface:high-availability-tenant-interface')

    api 'com.cmpay:lemon-framework-starter-security'
    implementation 'com.cmpay:lemon-framework-starter-datasource'
    api 'com.cmpay:lemon-framework-starter-session-hazelcast'
    api 'com.cmpay:lemon-framework-starter-context'

    implementation 'org.springframework.boot:spring-boot-starter-quartz'
    implementation 'org.springframework.plugin:spring-plugin-core:2.0.0.RELEASE'
    implementation 'org.apache.sshd:sshd-core:2.15.0'
    api ("com.baomidou:mybatis-plus-boot-starter:${versions.mybatisPlus}"){
        exclude group: 'org.springframework.boot'
    }
    implementation "com.baomidou:mybatis-plus-jsqlparser-4.9:${versions.mybatisPlus}"
    implementation "org.mapstruct:mapstruct:${versions.mapstruct}"
    implementation 'org.projectlombok:lombok-mapstruct-binding:0.2.0'
    implementation 'org.apache.commons:commons-collections4:4.5.0'
    implementation 'net.javacrumbs.shedlock:shedlock-spring:4.48.0'
    implementation 'net.javacrumbs.shedlock:shedlock-provider-hazelcast4:4.48.0'

    runtimeOnly 'com.mysql:mysql-connector-j'

    annotationProcessor "org.mapstruct:mapstruct-processor:${versions.mapstruct}"

    testAnnotationProcessor "org.mapstruct:mapstruct-processor:${versions.mapstruct}"

    implementation("cn.hutool:hutool-all:${hutoolVersion}")
}

configurations.all {
    resolutionStrategy.eachDependency { details ->
        if (details.requested.group == 'org.springframework.boot') {
            details.useVersion versions.springBoot
        }
    }
}