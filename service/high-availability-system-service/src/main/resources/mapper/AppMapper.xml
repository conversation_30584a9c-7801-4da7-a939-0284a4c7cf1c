<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.hacp.system.dao.IAppDao">
    <resultMap id="BaseResultMap" type="com.cmpay.hacp.system.entity.AppDO">
        <id column="app_id" property="appId" jdbcType="VARCHAR"/>
        <result column="app_name" property="appName" jdbcType="VARCHAR"/>
        <result column="dept_id" property="deptId" jdbcType="VARCHAR"/>
        <result column="app_owner_id" property="appOwnerId" jdbcType="VARCHAR"/>
        <result column="app_key" property="appKey" jdbcType="VARCHAR"/>
        <result column="salt" property="salt" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="create_user_id" property="createUserId" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP"/>
        <result column="token_key" property="tokenKey" jdbcType="VARCHAR"/>
        <result column="user_source" property="userSource" jdbcType="VARCHAR"/>
        <result column="sm_public_key" property="smPublicKey" jdbcType="VARCHAR"/>
        <result column="sm_private_key" property="smPrivateKey" jdbcType="VARCHAR"/>
    </resultMap>

    <resultMap id="ResultMapWithBLOBs" type="com.cmpay.hacp.system.entity.AppDO" extends="BaseResultMap">
        <result column="pub_key" property="pubKey" jdbcType="LONGVARCHAR"/>
        <result column="pri_key" property="priKey" jdbcType="LONGVARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        app_id,
        app_name,
        dept_id,
        app_owner_id,
        app_key,
        salt,
        status,
        remark,
        create_user_id,
        create_time,
        modify_time,
        token_key,
        user_source,
        sm_public_key,
        sm_private_key
    </sql>

    <sql id="Blob_Column_List">
        pub_key,
        pri_key
    </sql>

    <select id="get" resultMap="ResultMapWithBLOBs" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        ,
        <include refid="Blob_Column_List"/>
        from sys_app
        where app_id = #{appId,jdbcType=VARCHAR}
    </select>

    <delete id="delete" parameterType="java.lang.String">
        delete
        from sys_app
        where app_id = #{appId,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.cmpay.hacp.system.entity.AppDO">
        insert into sys_app
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="appId != null">
                app_id,
            </if>
            <if test="appName != null">
                app_name,
            </if>
            <if test="deptId != null">
                dept_id,
            </if>
            <if test="appOwnerId != null">
                app_owner_id,
            </if>
            <if test="appKey != null">
                app_key,
            </if>
            <if test="salt != null">
                salt,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="createUserId != null">
                create_user_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="modifyTime != null">
                modify_time,
            </if>
            <if test="tokenKey != null">
                token_key,
            </if>
            <if test="userSource != null">
                user_source,
            </if>
            <if test="smPublicKey != null">
                sm_public_key,
            </if>
            <if test="smPrivateKey != null">
                sm_private_key,
            </if>
            <if test="pubKey != null">
                pub_key,
            </if>
            <if test="priKey != null">
                pri_key,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="appId != null">
                #{appId,jdbcType=VARCHAR},
            </if>
            <if test="appName != null">
                #{appName,jdbcType=VARCHAR},
            </if>
            <if test="deptId != null">
                #{deptId,jdbcType=VARCHAR},
            </if>
            <if test="appOwnerId != null">
                #{appOwnerId,jdbcType=VARCHAR},
            </if>
            <if test="appKey != null">
                #{appKey,jdbcType=VARCHAR},
            </if>
            <if test="salt != null">
                #{salt,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="createUserId != null">
                #{createUserId,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null">
                #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="tokenKey != null">
                #{tokenKey,jdbcType=VARCHAR},
            </if>
            <if test="userSource != null">
                #{userSource,jdbcType=VARCHAR},
            </if>
            <if test="smPublicKey != null">
                #{smPublicKey,jdbcType=VARCHAR},
            </if>
            <if test="smPrivateKey != null">
                #{smPrivateKey,jdbcType=VARCHAR},
            </if>
            <if test="pubKey != null">
                #{pubKey,jdbcType=LONGVARCHAR},
            </if>
            <if test="priKey != null">
                #{priKey,jdbcType=LONGVARCHAR},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.cmpay.hacp.system.entity.AppDO">
        update sys_app
        <set>
            <if test="appName != null">
                app_name = #{appName,jdbcType=VARCHAR},
            </if>
            <if test="deptId != null">
                dept_id = #{deptId,jdbcType=VARCHAR},
            </if>
            <if test="appOwnerId != null">
                app_owner_id = #{appOwnerId,jdbcType=VARCHAR},
            </if>
            <if test="appKey != null">
                app_key = #{appKey,jdbcType=VARCHAR},
            </if>
            <if test="salt != null">
                salt = #{salt,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="createUserId != null">
                create_user_id = #{createUserId,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null">
                modify_time = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="tokenKey != null">
                token_key = #{tokenKey,jdbcType=VARCHAR},
            </if>
            <if test="userSource != null">
                user_source = #{userSource,jdbcType=VARCHAR},
            </if>
            <if test="smPublicKey != null">
                sm_public_key = #{smPublicKey,jdbcType=VARCHAR},
            </if>
            <if test="smPrivateKey != null">
                sm_private_key = #{smPrivateKey,jdbcType=VARCHAR},
            </if>
            <if test="pubKey != null">
                pub_key = #{pubKey,jdbcType=LONGVARCHAR},
            </if>
            <if test="priKey != null">
                pri_key = #{priKey,jdbcType=LONGVARCHAR},
            </if>
        </set>
        where app_id = #{appId,jdbcType=VARCHAR}
    </update>

    <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.cmpay.hacp.system.entity.AppDO">
        update sys_app
        set app_name       = #{appName,jdbcType=VARCHAR},
            dept_id = #{deptId,jdbcType=VARCHAR},
            app_owner_id   = #{appOwnerId,jdbcType=VARCHAR},
            app_key        = #{appKey,jdbcType=VARCHAR},
            salt           = #{salt,jdbcType=VARCHAR},
            status         = #{status,jdbcType=VARCHAR},
            remark         = #{remark,jdbcType=VARCHAR},
            create_user_id = #{createUserId,jdbcType=VARCHAR},
            create_time    = #{createTime,jdbcType=TIMESTAMP},
            modify_time    = #{modifyTime,jdbcType=TIMESTAMP},
            token_key      = #{tokenKey,jdbcType=VARCHAR},
            user_source    = #{userSource,jdbcType=VARCHAR},
            sm_public_key  = #{smPublicKey,jdbcType=VARCHAR},
            sm_private_key = #{smPrivateKey,jdbcType=VARCHAR},
            pub_key        = #{pubKey,jdbcType=LONGVARCHAR},
            pri_key        = #{priKey,jdbcType=LONGVARCHAR}
        where app_id = #{appId,jdbcType=VARCHAR}
    </update>

    <select id="find" resultMap="BaseResultMap" parameterType="com.cmpay.hacp.system.entity.AppDO">
        select
        <include refid="Base_Column_List"/>
        from sys_app
        <where>
            <if test="appId != null">
                and app_id = #{appId,jdbcType=VARCHAR}
            </if>
            <if test="appName != null">
                and app_name = #{appName,jdbcType=VARCHAR}
            </if>
            <if test="deptId != null">
                and dept_id = #{deptId,jdbcType=VARCHAR}
            </if>
            <if test="appOwnerId != null">
                and app_owner_id = #{appOwnerId,jdbcType=VARCHAR}
            </if>
            <if test="appKey != null">
                and app_key = #{appKey,jdbcType=VARCHAR}
            </if>
            <if test="salt != null">
                and salt = #{salt,jdbcType=VARCHAR}
            </if>
            <if test="status != null">
                and status = #{status,jdbcType=VARCHAR}
            </if>
            <if test="remark != null">
                and remark = #{remark,jdbcType=VARCHAR}
            </if>
            <if test="createUserId != null">
                and create_user_id = #{createUserId,jdbcType=VARCHAR}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="modifyTime != null">
                and modify_time = #{modifyTime,jdbcType=TIMESTAMP}
            </if>
            <if test="tokenKey != null">
                and token_key = #{tokenKey,jdbcType=VARCHAR}
            </if>
            <if test="userSource != null">
                and user_source = #{userSource,jdbcType=VARCHAR}
            </if>
            <if test="smPublicKey != null">
                and sm_public_key = #{smPublicKey,jdbcType=VARCHAR}
            </if>
            <if test="smPrivateKey != null">
                and sm_private_key = #{smPrivateKey,jdbcType=VARCHAR}
            </if>
            <if test="pubKey != null">
                and pub_key = #{pubKey,jdbcType=LONGVARCHAR}
            </if>
            <if test="priKey != null">
                and pri_key = #{priKey,jdbcType=LONGVARCHAR}
            </if>
        </where>
    </select>
</mapper>
