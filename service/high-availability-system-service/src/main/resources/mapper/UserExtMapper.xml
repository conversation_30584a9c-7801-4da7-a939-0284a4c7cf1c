<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.hacp.system.dao.IUserExtDao">
    <resultMap id="BaseResultMap" type="com.cmpay.hacp.system.entity.UserDO">
        <id column="user_id" property="userId" jdbcType="VARCHAR"/>
        <result column="user_name" property="userName" jdbcType="VARCHAR"/>
        <result column="full_name" property="fullName" jdbcType="VARCHAR"/>
        <result column="password" property="password" jdbcType="VARCHAR"/>
        <result column="salt" property="salt" jdbcType="VARCHAR"/>
        <result column="dept_id" property="deptId" jdbcType="VARCHAR"/>
        <result column="duty_id" property="dutyId" jdbcType="VARCHAR"/>
        <result column="email" property="email" jdbcType="VARCHAR"/>
        <result column="mobile" property="mobile" jdbcType="VARCHAR"/>
        <result column="weixin" property="weixin" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
        <result column="create_user_id" property="createUserId" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP"/>
        <result column="last_login_time" property="lastLoginTime" jdbcType="TIMESTAMP"/>
        <result column="has_role" property="hasRole" jdbcType="VARCHAR"/>
        <result column="cst_user_id" property="cstUserId" jdbcType="VARCHAR"/>
        <result column="app_id" property="appId" jdbcType="VARCHAR"/>
        <result column="pwd_modify_time" property="pwdModifyTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <resultMap id="BaseResultExtMap" type="com.cmpay.hacp.bo.system.UserBO">
        <id column="user_id" property="userId" jdbcType="VARCHAR"/>
        <result column="user_name" property="userName" jdbcType="VARCHAR"/>
        <result column="full_name" property="fullName" jdbcType="VARCHAR"/>
        <result column="password" property="password" jdbcType="VARCHAR"/>
        <result column="salt" property="salt" jdbcType="VARCHAR"/>
        <result column="dept_id" property="deptId" jdbcType="VARCHAR"/>
        <result column="duty_id" property="dutyId" jdbcType="BIGINT"/>
        <result column="email" property="email" jdbcType="VARCHAR"/>
        <result column="mobile" property="mobile" jdbcType="VARCHAR"/>
        <result column="weixin" property="weixin" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
        <result column="create_user_id" property="createUserId" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP"/>
        <result column="last_login_time" property="lastLoginTime" jdbcType="TIMESTAMP"/>
        <result column="has_role" property="hasRole" jdbcType="VARCHAR"/>
        <result column="cst_user_id" property="cstUserId" jdbcType="VARCHAR"/>
        <result column="app_id" property="appId" jdbcType="VARCHAR"/>
        <result column="pwd_modify_time" property="pwdModifyTime" jdbcType="TIMESTAMP"/>
        <result column="DEPT_NAME" property="deptName" jdbcType="VARCHAR"/>
    </resultMap>

    <resultMap id="BaseSimpleResultMap" type="com.cmpay.hacp.system.bo.system.SystemUserBO">
        <id column="user_id" property="userId" jdbcType="VARCHAR"/>
        <result column="user_name" property="userName" jdbcType="VARCHAR"/>
        <result column="full_name" property="fullName" jdbcType="VARCHAR"/>
    </resultMap>

    <resultMap id="BaseNoticeResultMap" type="com.cmpay.hacp.bo.system.UserBO">
        <id column="user_id" property="userId" jdbcType="VARCHAR"/>
        <result column="user_name" property="userName" jdbcType="VARCHAR"/>
        <result column="full_name" property="fullName" jdbcType="VARCHAR"/>
        <result column="email" property="email" jdbcType="VARCHAR"/>
        <result column="mobile" property="mobile" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        user_id,
        user_name,
        full_name,
        password,
        salt,
        dept_id,
        duty_id,
        email,
        mobile,
        weixin,
        status,
        create_user_id,
        create_time,
        modify_time,
        last_login_time,
        has_role,
        cst_user_id,
        app_id,
        pwd_modify_time
    </sql>
    <select id="queryUsers" resultMap="BaseResultExtMap">
        select u.user_id,
        u.user_name,
        u.scim_user_id,
        u.scim_user_name,
        u.full_name,
        u.password,
        u.salt,
        u.dept_id,
        u.duty_id,
        u.email,
        u.mobile,
        u.weixin,
        u.status,
        u.create_user_id,
        u.create_time,
        u.modify_time,
        u.last_login_time,
        u.has_role,
        u.cst_user_id,
        u.app_id,
        u.pwd_modify_time,
        d.dept_name
        from sys_user as u,
        sys_dept as d
        where u.dept_id = d.dept_id
        <if test="userDO.userId != null and userDO.userId != ''">
            and u.user_id = #{userDO.userId,jdbcType=VARCHAR}
        </if>
        <if test="userDO.userName != null and userDO.userName != ''">
            and u.user_name like concat('%',#{userDO.userName,jdbcType=VARCHAR},'%')
        </if>
        <if test="userDO.scimUserId != null and userDO.scimUserId != ''">
            and u.scim_user_id = #{userDO.scimUserId,jdbcType=VARCHAR}
        </if>
        <if test="userDO.scimUserName != null and userDO.scimUserName != ''">
            and u.scim_user_name = #{userDO.scimUserName,jdbcType=VARCHAR}
        </if>
        <if test="userDO.fullName != null and userDO.fullName != ''">
            and u.full_name = #{userDO.fullName,jdbcType=VARCHAR}
        </if>
        <if test="userDO.deptId != null and userDO.deptId != ''">
            and u.dept_id = #{userDO.deptId,jdbcType=VARCHAR}
        </if>
        <if test="userDO.dutyId != null and userDO.dutyId != ''">
            and u.duty_id = #{userDO.dutyId,jdbcType=VARCHAR}
        </if>
        <if test="userDO.email != null and userDO.email != ''">
            and u.email = #{userDO.email,jdbcType=VARCHAR}
        </if>
        <if test="userDO.mobile != null and userDO.mobile != ''">
            and u.mobile = #{userDO.mobile,jdbcType=VARCHAR}
        </if>
        <if test="userDO.weixin != null and userDO.weixin != ''">
            and u.weixin = #{userDO.weixin,jdbcType=VARCHAR}
        </if>
        <if test="userDO.status != null and userDO.status != ''">
            and u.status = #{userDO.status,jdbcType=VARCHAR}
        </if>
        <if test="userDO.hasRole != null and userDO.hasRole != ''">
            and u.has_role = #{userDO.hasRole,jdbcType=VARCHAR}
        </if>
        <if test="userDO.appId != null and userDO.appId != ''">
            and u.app_id = #{userDO.appId,jdbcType=VARCHAR}
        </if>
        <if test="deptIds != null and deptIds.size() != 0">
            and u.dept_id in
            <foreach item="deptId" collection="deptIds" open="(" close=")" separator=",">
                #{deptId,jdbcType=VARCHAR}
            </foreach>
        </if>
        order by u.modify_time desc
    </select>

    <select id="getAllUserMobiles" resultMap="BaseResultExtMap">
        select mobile, status
        from sys_user
        group by mobile, status
    </select>

    <select id="queryUsersByMobile" resultMap="BaseResultExtMap">
        select user_id
        from sys_user
        where mobile = #{mobile,jdbcType=VARCHAR}
    </select>

    <update id="updateUserByMobile" parameterType="com.cmpay.hacp.system.entity.UserDO">
        update sys_user
        <set>
            <if test="scimUserId != null">
                scim_user_id = #{scimUserId,jdbcType=VARCHAR},
            </if>
            <if test="scimUserName != null">
                scim_user_name = #{scimUserName,jdbcType=VARCHAR},
            </if>
            <if test="userName != null">
                user_name = #{userName,jdbcType=VARCHAR},
            </if>
            <if test="fullName != null">
                full_name = #{fullName,jdbcType=VARCHAR},
            </if>
            <if test="password != null">
                password = #{password,jdbcType=VARCHAR},
            </if>
            <if test="salt != null">
                salt = #{salt,jdbcType=VARCHAR},
            </if>
            <if test="deptId != null and deptId != ''">
                dept_id = #{deptId,jdbcType=VARCHAR},
            </if>
            <if test="dutyId != null">
                duty_id = #{dutyId,jdbcType=VARCHAR},
            </if>
            <if test="email != null">
                email = #{email,jdbcType=VARCHAR},
            </if>
            <if test="userId != null">
                user_id = #{userId,jdbcType=VARCHAR},
            </if>
            <if test="weixin != null">
                weixin = #{weixin,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="createUserId != null">
                create_user_id = #{createUserId,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null">
                modify_time = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastLoginTime != null">
                last_login_time = #{lastLoginTime,jdbcType=TIMESTAMP},
            </if>
            <if test="hasRole != null">
                has_role = #{hasRole,jdbcType=VARCHAR},
            </if>
            <if test="cstUserId != null">
                cst_user_id = #{cstUserId,jdbcType=VARCHAR},
            </if>
            <if test="appId != null">
                app_id = #{appId,jdbcType=VARCHAR},
            </if>
            <if test="pwdModifyTime != null">
                pwd_modify_time = #{pwdModifyTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where mobile = #{mobile,jdbcType=VARCHAR}
    </update>
    <update id="disable">
        update sys_user
        set status='DISABLE',
            modify_time = #{modifyTime,jdbcType=TIMESTAMP}
        where user_id = #{userId,jdbcType=VARCHAR}
    </update>
    <update id="enable">
        update sys_user
        set status='ENABLE',
            modify_time = #{modifyTime,jdbcType=TIMESTAMP}
        where user_id = #{userId,jdbcType=VARCHAR}
    </update>


    <select id="getSystemUsers" parameterType="com.cmpay.hacp.system.bo.system.SystemUserBO" resultMap="BaseSimpleResultMap">
        select
        user_id,
        user_name,
        full_name
        from sys_user
        <where>
            <if test="userId != null and userId != ''">
                and user_id = #{userId,jdbcType=VARCHAR}
            </if>
            <if test="userName != null and userName != ''">
                and user_name = #{userName,jdbcType=VARCHAR}
            </if>
            <if test="fullName != null and fullName != ''">
                and full_name = #{fullName,jdbcType=VARCHAR}
            </if>
            <if test="status != null and status != ''">
                and status = #{status,jdbcType=VARCHAR}
            </if>
            <if test="hasRole != null and hasRole != ''">
                and has_role = #{hasRole,jdbcType=VARCHAR}
            </if>
        </where>
        order by create_time asc
    </select>

    <update id="changeOwnSystemUserHasRole" parameterType="com.cmpay.hacp.system.bo.system.SystemUserBO">
        update sys_user
        set has_role    = 'Y',
            status      = 'ENABLE',
            modify_time = #{modifyTime,jdbcType=TIMESTAMP}
        where user_id = #{userId,jdbcType=VARCHAR}
    </update>

    <select id="findSystemUserId" parameterType="com.cmpay.hacp.system.bo.system.SystemUserBO"
            resultType="java.lang.String">
        select
        user_id
        from sys_user
        <where>
            <if test="scimUserName != null and scimUserName != ''">
                and scim_user_name = #{scimUserName,jdbcType=VARCHAR}
            </if>
            <if test="mobile != null and mobile != ''">
                and mobile = #{mobile,jdbcType=VARCHAR}
            </if>
            <if test="fullName != null and fullName != ''">
                and full_name = #{fullName,jdbcType=VARCHAR}
            </if>
        </where>
        order by create_time asc
    </select>
    <select id="getUsersByUserId" resultMap="BaseNoticeResultMap">
        select
            user_id,
            user_name,
            full_name,
            email,
            mobile
        from sys_user
        <where>
            <if test="list != null and list.size() > 0">
                user_id in
                <foreach collection="list" item="userId" open="(" separator="," close=")">
                    #{userId}
                </foreach>
            </if>
        </where>
    </select>
</mapper>
