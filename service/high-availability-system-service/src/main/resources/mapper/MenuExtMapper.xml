<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.hacp.system.dao.IMenuExtDao">
    <resultMap id="BaseResultMap" type="com.cmpay.hacp.system.entity.MenuDO">
        <id column="menu_id" property="menuId" jdbcType="BIGINT"/>
        <result column="parent_id" property="parentId" jdbcType="BIGINT"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="app_id" property="appId" jdbcType="VARCHAR"/>
        <result column="url" property="url" jdbcType="VARCHAR"/>
        <result column="perms" property="perms" jdbcType="VARCHAR"/>
        <result column="type" property="type" jdbcType="VARCHAR"/>
        <result column="icon" property="icon" jdbcType="VARCHAR"/>
        <result column="order_num" property="orderNum" jdbcType="BIGINT"/>
        <result column="create_user_id" property="createUserId" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP"/>
        <result column="meta" property="meta" jdbcType="VARCHAR"/>
        <result column="component" property="component" jdbcType="VARCHAR"/>
        <result column="redirect" property="redirect" jdbcType="VARCHAR"/>
        <result column="en_name" property="enName" jdbcType="VARCHAR"/>
        <result column="parent_name" property="parentName" jdbcType="VARCHAR"/>
        <result column="hide_title" property="hideTitle" jdbcType="BOOLEAN"/>
        <result column="hidden" property="hidden" jdbcType="BOOLEAN"/>
        <result column="hide_children" property="hideChildren" jdbcType="BOOLEAN"/>
        <result column="keepalive" property="keepalive" jdbcType="BOOLEAN"/>
        <result column="hide_page_title_bar" property="hidePageTitleBar" jdbcType="BOOLEAN"/>
    </resultMap>

    <resultMap id="BaseResultExtMap" type="com.cmpay.hacp.bo.menu.MenuBO">
        <id column="menu_id" property="menuId" jdbcType="BIGINT"/>
        <result column="parent_id" property="parentId" jdbcType="BIGINT"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="app_id" property="appId" jdbcType="VARCHAR"/>
        <result column="url" property="url" jdbcType="VARCHAR"/>
        <result column="perms" property="perms" jdbcType="VARCHAR"/>
        <result column="type" property="type" jdbcType="VARCHAR"/>
        <result column="icon" property="icon" jdbcType="VARCHAR"/>
        <result column="order_num" property="orderNum" jdbcType="BIGINT"/>
        <result column="create_user_id" property="createUserId" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP"/>
        <result column="meta" property="meta" jdbcType="VARCHAR"/>
        <result column="component" property="component" jdbcType="VARCHAR"/>
        <result column="redirect" property="redirect" jdbcType="VARCHAR"/>
        <result column="en_name" property="enName" jdbcType="VARCHAR"/>
        <result column="parent_name" property="parentName" jdbcType="VARCHAR"/>
        <result column="hide_title" property="hideTitle" jdbcType="BOOLEAN"/>
        <result column="hidden" property="hidden" jdbcType="BOOLEAN"/>
        <result column="hide_children" property="hideChildren" jdbcType="BOOLEAN"/>
        <result column="keepalive" property="keepalive" jdbcType="BOOLEAN"/>
        <result column="hide_page_title_bar" property="hidePageTitleBar" jdbcType="BOOLEAN"/>
    </resultMap>

    <sql id="Base_Column_List">
        menu_id,
        parent_id,
        name,
        app_id,
        url,
        perms,
        type,
        icon,
        order_num,
        create_user_id,
        create_time,
        modify_time,
        meta,
        component,
        redirect,
        en_name,
        parent_name,
        hide_title,
        hidden,
        hide_children,
        keepalive,
        hide_page_title_bar
    </sql>

    <select id="queryUserMenus" parameterType="java.lang.String" resultMap="BaseResultExtMap">
        select t1.menu_id,
               t1.parent_id,
               t1.name,
               t1.app_id,
               t1.url,
               t1.perms,
               t1.type,
               t1.icon,
               t1.order_num,
               t1.create_user_id,
               t1.create_time,
               t1.modify_time,
               t1.meta,
               t1.component,
               t1.redirect,
               t1.en_name,
               t1.parent_name,
               t1.hide_title,
               t1.hidden,
               t1.hide_children,
               t1.keepalive,
               t1.hide_page_title_bar
        from sys_menu as t1
        where t1.menu_id in (select distinct m.menu_id
                             from sys_menu as m,
                                  sys_role_menu as r,
                                  sys_user_role as u
                             where m.menu_id = r.menu_id
                               and r.role_id = u.role_id
                               and u.user_id = #{userId,jdbcType=VARCHAR})
        order by t1.parent_id asc, t1.menu_id asc, t1.order_num asc
    </select>

    <select id="queryAppMenus" parameterType="com.cmpay.hacp.system.entity.MenuDO" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from sys_menu
        <where>
            <if test="menuDO.menuId != null">
                and menu_id = #{menuDO.menuId,jdbcType=BIGINT}
            </if>
            <if test="menuDO.parentId != null">
                and parent_id = #{menuDO.parentId,jdbcType=BIGINT}
            </if>
            <if test="menuDO.name != null">
                and name = #{menuDO.name,jdbcType=VARCHAR}
            </if>
            <if test="menuDO.appId != null">
                and app_id = #{menuDO.appId,jdbcType=VARCHAR}
            </if>
            <if test="menuDO.url != null">
                and url = #{menuDO.url,jdbcType=VARCHAR}
            </if>
            <if test="menuDO.perms != null">
                and perms = #{menuDO.perms,jdbcType=VARCHAR}
            </if>
            <if test="menuDO.type != null">
                and type = #{menuDO.type,jdbcType=VARCHAR}
            </if>
            <if test="menuDO.icon != null">
                and icon = #{menuDO.icon,jdbcType=VARCHAR}
            </if>
            <if test="menuDO.orderNum != null">
                and order_num = #{menuDO.orderNum,jdbcType=BIGINT}
            </if>
            <if test="menuDO.createUserId != null">
                and create_user_id = #{menuDO.createUserId,jdbcType=VARCHAR}
            </if>
            <if test="menuDO.createTime != null">
                and create_time = #{menuDO.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="menuDO.modifyTime != null">
                and modify_time = #{menuDO.modifyTime,jdbcType=TIMESTAMP}
            </if>
            <if test="menuDO.meta != null">
                and meta = #{menuDO.meta,jdbcType=VARCHAR}
            </if>
            <if test="menuDO.component != null">
                and component = #{menuDO.component,jdbcType=VARCHAR}
            </if>
            <if test="menuDO.redirect != null">
                and redirect = #{menuDO.redirect,jdbcType=VARCHAR}
            </if>
            <if test="menuDO.enName != null">
                and en_name = #{menuDO.enName,jdbcType=VARCHAR}
            </if>
            <if test="menuDO.parentName != null">
                and parent_name = #{menuDO.parentName,jdbcType=VARCHAR}
            </if>
            <if test="menuDO.hideTitle != null">
                and hide_title = #{menuDO.hideTitle,jdbcType=BOOLEAN}
            </if>
            <if test="menuDO.hidden != null">
                and hidden = #{menuDO.hidden,jdbcType=BOOLEAN}
            </if>
            <if test="menuDO.hideChildren != null">
                and hide_children = #{menuDO.hideChildren,jdbcType=BOOLEAN}
            </if>
            <if test="menuDO.keepalive != null">
                and keepalive = #{menuDO.keepalive,jdbcType=BOOLEAN}
            </if>
            <if test="menuDO.hidePageTitleBar != null">
                and hide_page_title_bar = #{menuDO.hidePageTitleBar,jdbcType=BOOLEAN}
            </if>
        </where>
        order by menu_id asc, parent_id asc, order_num asc
    </select>
</mapper>
