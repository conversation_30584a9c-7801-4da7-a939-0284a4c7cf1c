/*
 * @ClassName AppDO
 * @Description
 * @version 1.0
 * @Date 2021-09-02 15:24:38
 */
package com.cmpay.hacp.system.entity;

import com.cmpay.lemon.framework.annotation.DataObject;

import java.time.LocalDateTime;

@DataObject
public class AppDO extends BaseDO {
    /**
     * @Fields appId 应用系统编码
     */
    private String appId;
    /**
     * @Fields appName 应用系统中文名称
     */
    private String appName;
    /**
     * @Fields deptId 部门id
     */
    private String deptId;
    /**
     * @Fields appOwnerId 应用负责人
     */
    private String appOwnerId;
    /**
     * @Fields appKey 应用系统秘钥
     */
    private String appKey;
    /**
     * @Fields salt 应用密钥加盐
     */
    private String salt;
    /**
     * @Fields status DISABLE:禁用  ENABLE:启用
     */
    private String status;
    /**
     * @Fields remark 备注
     */
    private String remark;
    /**
     * @Fields createUserId 创建者id
     */
    private String createUserId;
    /**
     * @Fields createTime 创建时间
     */
    private LocalDateTime createTime;
    /**
     * @Fields modifyTime 更新时间
     */
    private LocalDateTime modifyTime;
    /**
     * @Fields tokenKey 访问token申请的key
     */
    private String tokenKey;
    /**
     * @Fields userSource SELF_REGISTER 用户自己注册 ADMIN_REGISTER 系统注册
     */
    private String userSource;
    /**
     * @Fields smPublicKey SM公钥
     */
    private String smPublicKey;
    /**
     * @Fields smPrivateKey SM私钥
     */
    private String smPrivateKey;
    /**
     * @Fields pubKey 应用公钥
     */
    private String pubKey;
    /**
     * @Fields priKey 应用私钥
     */
    private String priKey;

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getAppName() {
        return appName;
    }

    public void setAppName(String appName) {
        this.appName = appName;
    }

    public String getDeptId() {
        return deptId;
    }

    public void setDeptId(String deptId) {
        this.deptId = deptId;
    }

    public String getAppOwnerId() {
        return appOwnerId;
    }

    public void setAppOwnerId(String appOwnerId) {
        this.appOwnerId = appOwnerId;
    }

    public String getAppKey() {
        return appKey;
    }

    public void setAppKey(String appKey) {
        this.appKey = appKey;
    }

    public String getSalt() {
        return salt;
    }

    public void setSalt(String salt) {
        this.salt = salt;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getCreateUserId() {
        return createUserId;
    }

    public void setCreateUserId(String createUserId) {
        this.createUserId = createUserId;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(LocalDateTime modifyTime) {
        this.modifyTime = modifyTime;
    }

    public String getTokenKey() {
        return tokenKey;
    }

    public void setTokenKey(String tokenKey) {
        this.tokenKey = tokenKey;
    }

    public String getUserSource() {
        return userSource;
    }

    public void setUserSource(String userSource) {
        this.userSource = userSource;
    }

    public String getSmPublicKey() {
        return smPublicKey;
    }

    public void setSmPublicKey(String smPublicKey) {
        this.smPublicKey = smPublicKey;
    }

    public String getSmPrivateKey() {
        return smPrivateKey;
    }

    public void setSmPrivateKey(String smPrivateKey) {
        this.smPrivateKey = smPrivateKey;
    }

    public String getPubKey() {
        return pubKey;
    }

    public void setPubKey(String pubKey) {
        this.pubKey = pubKey;
    }

    public String getPriKey() {
        return priKey;
    }

    public void setPriKey(String priKey) {
        this.priKey = priKey;
    }
}