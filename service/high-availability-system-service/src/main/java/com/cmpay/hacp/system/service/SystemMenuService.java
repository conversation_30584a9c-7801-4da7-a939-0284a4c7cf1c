package com.cmpay.hacp.system.service;


import com.cmpay.hacp.bo.menu.MenuBO;
import com.cmpay.hacp.system.bo.menu.PermMenuTreeBO;
import com.cmpay.hacp.system.bo.menu.PermMenuTreeMetaBO;
import com.cmpay.hacp.system.bo.system.DictBO;
import com.cmpay.lemon.framework.page.PageInfo;

import java.util.List;

/**
 * 描述
 *
 * <AUTHOR> tnw
 */
public interface SystemMenuService {
    /**
     * 获取用户菜单
     *
     * @param userId
     * @return
     */
    PermMenuTreeMetaBO getUserMenus(String userId);

    /**
     * 获取应用菜单
     *
     * @return
     */
    PermMenuTreeBO getAppMenus();

    /**
     * 查询所有菜单
     *
     * @param pageNum
     * @param pageSize
     * @param userId
     * @return
     */
    PageInfo<MenuBO> page(int pageNum, int pageSize, String userId);


    /**
     * 根据Id查找菜单信息
     *
     * @param menuId
     * @return
     */
    MenuBO getMenuById(Long menuId);


    /**
     * @param operatorId
     * @param menuBO
     */
    void add(String operatorId, MenuBO menuBO);

    /**
     * @param operatorId
     * @param menuBO
     */
    void update(String operatorId, MenuBO menuBO);

    /**
     * 删除
     *
     * @param menuId
     */
    void delete(Long menuId);


    /**
     * 批量删除菜单ID
     *
     * @param menuIds
     */
    void delete(List<Long> menuIds);

    /**
     * 查询菜单类型
     *
     * @return
     */
    List<DictBO> type();

    /**
     * @param menuBO
     */
    void verifyFormData(MenuBO menuBO);

}
