/*
 * @ClassName RiseSystemLogDO
 * @Description
 * @version 1.0
 * @Date 2023-08-08 16:10:59
 */
package com.cmpay.hacp.system.bo.system;

import com.cmpay.hacp.system.log.bo.SysStaticLogBO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * 系统日志
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SystemLogBO extends SysStaticLogBO {

    /**
     * @Fields tenantId 租户ID
     */
    private String tenantId;

    /**
     * @Fields tenantName 租户名称
     */
    private String tenantName;

    /**
     * @Fields workspaceId 项目ID
     */
    private String workspaceId;

    /**
     * @Fields workspaceName 项目名称
     */
    private String workspaceName;

}
