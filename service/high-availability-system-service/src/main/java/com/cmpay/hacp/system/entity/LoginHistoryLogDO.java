
package com.cmpay.hacp.system.entity;

import com.cmpay.hacp.annotation.mybatis.CryptField;
import com.cmpay.hacp.annotation.mybatis.CryptType;
import com.cmpay.lemon.framework.annotation.DataObject;

import java.time.LocalDate;
import java.time.LocalDateTime;

@DataObject
public class LoginHistoryLogDO extends BaseDO {
    /**
     * @Fields id 主键ID
     */
    private String id;
    /**
     * @Fields userId 用户ID
     */
    private String userId;
    /**
     * @Fields userName 用户名
     */
    private String userName;
    /**
     * @Fields name 姓名
     */
    @CryptField(type = CryptType.SM4)
    private String name;
    /**
     * @Fields mobile 手机号码
     */
    @CryptField(type = CryptType.SM4)
    private String mobile;
    /**
     * @Fields loginIp 登录IP
     */
    private String loginIp;
    /**
     * @Fields loginTerminal 登录终端设备信息 user_agent
     */
    private String loginTerminal;
    /**
     * @Fields loginFrom 登录来源
     */
    private String loginFrom;
    /**
     * @Fields loginDate 登录日期
     */
    private LocalDate loginDate;
    /**
     * @Fields loginTime 登录时间
     */
    private LocalDateTime loginTime;
    /**
     * @Fields requestId 日志流水号
     */
    private String requestId;
    /**
     * @Fields isUse 是否可用(0-否,1-是)
     */
    private Short isUse;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getLoginIp() {
        return loginIp;
    }

    public void setLoginIp(String loginIp) {
        this.loginIp = loginIp;
    }

    public String getLoginTerminal() {
        return loginTerminal;
    }

    public void setLoginTerminal(String loginTerminal) {
        this.loginTerminal = loginTerminal;
    }

    public String getLoginFrom() {
        return loginFrom;
    }

    public void setLoginFrom(String loginFrom) {
        this.loginFrom = loginFrom;
    }

    public LocalDate getLoginDate() {
        return loginDate;
    }

    public void setLoginDate(LocalDate loginDate) {
        this.loginDate = loginDate;
    }

    public LocalDateTime getLoginTime() {
        return loginTime;
    }

    public void setLoginTime(LocalDateTime loginTime) {
        this.loginTime = loginTime;
    }

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public Short getIsUse() {
        return isUse;
    }

    public void setIsUse(Short isUse) {
        this.isUse = isUse;
    }
}
