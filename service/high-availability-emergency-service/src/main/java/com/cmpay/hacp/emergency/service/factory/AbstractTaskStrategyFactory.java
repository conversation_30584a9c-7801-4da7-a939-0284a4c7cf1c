package com.cmpay.hacp.emergency.service.factory;

import cn.hutool.extra.spring.SpringUtil;
import com.cmpay.hacp.emergency.bo.HacpCaseVariableBO;
import com.cmpay.hacp.bo.task.HacpEmergencyTaskBO;
import com.cmpay.hacp.bo.task.TaskParam;
import com.cmpay.hacp.capable.TaskEncryptCapable;
import com.cmpay.hacp.enums.MsgEnum;
import com.cmpay.hacp.emergency.exception.TaskStrategyFactoryException;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.bpmn.instance.BaseElement;

import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * @description:
 * <AUTHOR>
 * @date 2024/5/23 15:29
 * @version 1.0
 */
@Data
@Slf4j
public abstract class AbstractTaskStrategyFactory{

    public static HashMap<String, TaskStrategyType> map;

    public TaskStrategyType taskType;

    private static List<String> RETRY_AUTO_COMPLETE= new ArrayList<>(8);

    private List<TaskStrategyType> taskStrategies;

    static {
        map = new HashMap<>();
    }

    public AbstractTaskStrategyFactory(@NotNull List<TaskStrategyType> taskStrategies) {
        this.taskStrategies = taskStrategies;
        addStrategy(taskStrategies);
    }

    public void addStrategy(List<TaskStrategyType> taskStrategies) {
        taskStrategies.forEach(f->{
            if(map.containsKey(f.getCode())){
                log.error("old clazz :{}", map.get(f.getCode()).getClazz());
                throw new TaskStrategyFactoryException("任务类型策略重复："+f.getCode());
            }
            map.put(f.getCode(), f);
        });

    }

    /**
     * 检查参数是否合法
     * true合法
     * @param json
     * @return
     */
    public abstract boolean checkTaskParam(String json);

    public abstract TaskParam toTaskParam(String json);

    public abstract  <T extends BaseElement> void bpmnTaskFieldHandle(T node, HacpEmergencyTaskBO taskInfo, BpmnModelInstance modelInstance);

    /**
     * 是否需要加密字段
     * @return
     */
    public abstract boolean isNeedEncrypt();

    /**
     * 屏蔽加密字段
     * @param json
     * @return
     */
    public String toMaskField(String json){
        return json;
    };

    public String toFieldEncrypt(TaskEncryptCapable taskBO,ExtState extState){
        return taskBO.getTaskParam();
    }

    public void toFieldDecrypt(HacpEmergencyTaskBO taskBO){

    }

    public void taskParamArchive(HacpCaseVariableBO taskBO,String workspaceId,String businessKey){

    }

    public abstract String getBpmTaskType();

    public abstract String getActivityType();

    /**
     *
     * @param taskType 任务类型枚举
     * @return 校验处理实现类
     */

    public static  AbstractTaskStrategyFactory newInstance(String taskType) {
        TaskStrategyType taskStrategyType = map.get(taskType);
        if (JudgeUtils.isNull(taskStrategyType)) {
            BusinessException.throwBusinessException(MsgEnum.CASE_PROCESS_CHECK_FAILED);
        }
        AbstractTaskStrategyFactory bean = SpringUtil.getBean(taskStrategyType.getClazz(), AbstractTaskStrategyFactory.class);
        bean.setTaskType(taskStrategyType);
        return bean;
    }

    /**
     * 动态配置时为false
     * @param taskParamJson
     * @return
     */
    public boolean isNotDynamicParam(String taskParamJson) {
        return true;
    }

    protected static void addRetryAutoComplete(String code){
        RETRY_AUTO_COMPLETE.add(code);
    }

    public static boolean isRetryAutoComplete(String code) {
        return RETRY_AUTO_COMPLETE.contains(code);
    }

    public enum ExtState{
        DEFAULT,
        TASK,
        TASK_ADD,
        TASK_UPDATE,
        CASE,
        CASE_START;
    }
}
