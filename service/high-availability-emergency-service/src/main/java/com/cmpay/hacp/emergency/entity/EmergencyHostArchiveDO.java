/*
 * @ClassName EmergencyHostArchiveDO
 * @Description 
 * @version 1.0
 * @Date 2024-10-30 17:37:41
 */
package com.cmpay.hacp.emergency.entity;

import com.cmpay.framework.data.BaseDO;
import com.cmpay.hacp.enums.HostOSEnum;
import com.cmpay.lemon.framework.annotation.DataObject;

@DataObject
public class EmergencyHostArchiveDO extends BaseDO {
    /**
     * @Fields id 
     */
    private Long id;
    /**
     * @Fields hostId 主机id
     */
    private Long hostId;
    /**
     * @Fields activityId 流程任务编号
     */
    private String activityId;
    /**
     * @Fields businessKey 绑定的业务id
     */
    private String businessKey;
    /**
     * @Fields workspaceId 项目空间ID
     */
    private String workspaceId;
    /**
     * @Fields hostDesc 机器描述
     */
    private String hostDesc;
    /**
     * @Fields hostAddress 机器地址
     */
    private String hostAddress;
    /**
     * @Fields hostPort port
     */
    private Integer hostPort;
    /**
     * @Fields hostUsername 账号
     */
    private String hostUsername;
    /**
     * @Fields hostPassword 密码
     */
    private String hostPassword;
    /**
     * @Fields secretKey 密钥
     */
    private String secretKey;
    /**
     * @Fields hostOs 主机系统
     */
    private HostOSEnum hostOS;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getHostId() {
        return hostId;
    }

    public void setHostId(Long hostId) {
        this.hostId = hostId;
    }

    public String getActivityId() {
        return activityId;
    }

    public void setActivityId(String activityId) {
        this.activityId = activityId;
    }

    public String getBusinessKey() {
        return businessKey;
    }

    public void setBusinessKey(String businessKey) {
        this.businessKey = businessKey;
    }

    public String getWorkspaceId() {
        return workspaceId;
    }

    public void setWorkspaceId(String workspaceId) {
        this.workspaceId = workspaceId;
    }

    public String getHostDesc() {
        return hostDesc;
    }

    public void setHostDesc(String hostDesc) {
        this.hostDesc = hostDesc;
    }

    public String getHostAddress() {
        return hostAddress;
    }

    public void setHostAddress(String hostAddress) {
        this.hostAddress = hostAddress;
    }

    public Integer getHostPort() {
        return hostPort;
    }

    public void setHostPort(Integer hostPort) {
        this.hostPort = hostPort;
    }

    public String getHostUsername() {
        return hostUsername;
    }

    public void setHostUsername(String hostUsername) {
        this.hostUsername = hostUsername;
    }

    public String getHostPassword() {
        return hostPassword;
    }

    public void setHostPassword(String hostPassword) {
        this.hostPassword = hostPassword;
    }

    public String getSecretKey() {
        return secretKey;
    }

    public void setSecretKey(String secretKey) {
        this.secretKey = secretKey;
    }

    public HostOSEnum getHostOS() {
        return hostOS;
    }

    public void setHostOS(HostOSEnum hostOS) {
        this.hostOS = hostOS;
    }
}