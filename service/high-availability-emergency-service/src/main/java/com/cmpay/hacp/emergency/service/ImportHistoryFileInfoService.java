package com.cmpay.hacp.emergency.service;

import com.cmpay.hacp.emergency.bo.EmergencyHostBO;
import com.cmpay.hacp.emergency.bo.ImportHistoryFileInfoBO;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2024/08/30 14:16
 * @since 1.0.0
 */

public interface ImportHistoryFileInfoService {

    /**
     * 保存导入历史信息
     *
     * @param importHistoryFileInfo 基本信息
     * @param file
     * @param dataList
     */
    int addImportHistoryFileInfo(ImportHistoryFileInfoBO importHistoryFileInfo, MultipartFile file, List<EmergencyHostBO> dataList);

    /**
     * 文件下载
     *
     * @param id
     * @return
     */
    ImportHistoryFileInfoBO getImportHistoryFileInfo(Integer id);
}
