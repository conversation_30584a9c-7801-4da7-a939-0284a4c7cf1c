package com.cmpay.hacp.emergency.bo.process;

import com.cmpay.lemon.framework.page.PageInfo;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * @description:
 * <AUTHOR>
 * @date 2024/5/31 10:03
 * @version 1.0
 */

@Getter
@Setter
public class TaskExecuteLogPage extends PageInfo<String> {

    private String taskStatus;

    private int index;

    public TaskExecuteLogPage(List<String> list) {
        super(list);
    }
}
