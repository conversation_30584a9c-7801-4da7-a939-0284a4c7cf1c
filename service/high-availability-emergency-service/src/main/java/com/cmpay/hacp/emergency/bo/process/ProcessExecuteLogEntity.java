package com.cmpay.hacp.emergency.bo.process;

import com.hazelcast.collection.IList;
import com.hazelcast.collection.IQueue;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;

/**
 * @description:
 * <AUTHOR>
 * @date 2024/5/31 10:03
 * @version 1.0
 */

@Getter
@Setter
@Slf4j
public class ProcessExecuteLogEntity {

    private StringBuffer logString = new StringBuffer();

    private ThreadLocal<IQueue<String>> logQueue;

    private ThreadLocal<IList<String>> logList;

    //异步追加输出到自定义的Buffer
    public ProcessExecuteLogEntity appendBuffer(String content, LogLevel level){
        StringBuilder append = new StringBuilder().append(LocalDateTime.now()).append(" [").append(level.toString()).append("] ").append(content).append("\n");
        append(append);
        return this;
    }

    public ProcessExecuteLogEntity appendTrace(Throwable t){
        StringBuilder append = new StringBuilder().append(t);
        // Print our stack trace
        StackTraceElement[] trace = t.getStackTrace();
        for (StackTraceElement traceElement : trace)
            append.append("\n\tat ").append(traceElement);
        append.append("\n");
        append(append);
        return this;
    }

    private void append(StringBuilder append) {
        this.logString.append(append);
        boolean offer = logQueue.get().offer(append.toString());
        if (!offer) {
            this.logString.append(LocalDateTime.now()).append(" [").append(LogLevel.WARN).append("] ").append("实时日志推送队列失败！").append("\n");
        }
    }

    public ProcessExecuteLogEntity appendBuffer(String content){
        return appendBuffer(content, LogLevel.INFO);
    }
    @Override
    public String toString(){
        return this.logString.toString();
    }

    public enum LogLevel{
        INFO, WARN, ERROR, DEBUG;
    }

    public ProcessExecuteLogEntity(IQueue<String> logQueue, IList<String> logList) {
        this.logQueue = ThreadLocal.withInitial(()->logQueue);
        if (!logQueue.isEmpty()) {
            logQueue.clear();
        }
        this.logList = ThreadLocal.withInitial(()->logList);
        if (!logList.isEmpty()) {
            logList.clear();
        }
    }
}
