package com.cmpay.hacp.emergency.service.factory.impl;

import com.cmpay.hacp.bo.task.HacpEmergencyTaskBO;
import com.cmpay.hacp.bo.task.TaskParam;
import com.cmpay.hacp.emergency.service.factory.AbstractTaskStrategyFactory;
import com.cmpay.hacp.emergency.service.factory.TaskStrategyType;
import com.cmpay.hacp.enums.MsgEnum;
import com.cmpay.hacp.emergency.utils.CamundaUtil;
import com.cmpay.hacp.utils.JsonUtil;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import lombok.extern.slf4j.Slf4j;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.bpmn.instance.BaseElement;
import org.camunda.bpm.model.bpmn.instance.ExtensionElements;
import org.camunda.bpm.model.bpmn.instance.UserTask;
import org.camunda.bpm.model.bpmn.instance.camunda.CamundaTaskListener;
import org.springframework.stereotype.Service;

import java.util.Collections;

/**
 * @description:
 * <AUTHOR>
 * @date 2024/6/4 9:44
 * @version 1.0
 */
@Slf4j
@Service(value = ManualOperationImpl.BEAN_NAME)
public class ManualOperationImpl extends AbstractTaskStrategyFactory {

    protected final static String BEAN_NAME = "manualOperationImpl";
    public ManualOperationImpl() {
        super(Collections.singletonList(new TaskStrategyType("1", "手动审批", BEAN_NAME)));
        addRetryAutoComplete("1");
    }

    @Override
    public <T extends BaseElement> void bpmnTaskFieldHandle(T node, HacpEmergencyTaskBO taskInfo, BpmnModelInstance modelInstance) {
        if (JudgeUtils.isBlank(taskInfo.getTaskOperator())) {
            if (JudgeUtils.isNotBlank(taskInfo.getTaskParam())) {
                taskInfo.setTaskOperator(JsonUtil.strToObject(taskInfo.getTaskParam(), HacpEmergencyTaskBO.class).getTaskOperator());
            } else {
                BusinessException.throwBusinessException(MsgEnum.TASK_OPERATOR_IS_NULL);
            }
        }

        //设置设置任务审批人
        if (node instanceof UserTask) {
            ((UserTask)node).setCamundaAssignee(taskInfo.getTaskOperator());
            ((UserTask)node).setName(taskInfo.getTaskName());
            //设置任务监听器
            ExtensionElements extensionElements = node.getExtensionElements();
            if (extensionElements != null) {
                boolean match = extensionElements.getElementsQuery()
                        .filterByType(CamundaTaskListener.class).list().stream()
                        .anyMatch(camundaTaskListener ->
                                JudgeUtils.equalsAny(camundaTaskListener.getCamundaEvent(),"create","complete","delete"));
                if (!match) {
                    CamundaTaskListener camundaTaskListener = extensionElements.addExtensionElement(CamundaTaskListener.class);
                    camundaTaskListener.setCamundaEvent("create");
                    camundaTaskListener.setCamundaClass("com.cmpay.hacp.emergency.service.camunda.listener.UserTaskCreateListener");
                    node.setExtensionElements(extensionElements);
                    CamundaTaskListener completeTaskListener = extensionElements.addExtensionElement(CamundaTaskListener.class);
                    completeTaskListener.setCamundaEvent("complete");
                    completeTaskListener.setCamundaClass("com.cmpay.hacp.emergency.service.camunda.listener.UserTaskCompleteListener");
                    node.setExtensionElements(extensionElements);
                    CamundaTaskListener deleteTaskListener = extensionElements.addExtensionElement(CamundaTaskListener.class);
                    deleteTaskListener.setCamundaEvent("delete");
                    deleteTaskListener.setCamundaClass("com.cmpay.hacp.emergency.service.camunda.listener.UserTaskDeleteListener");
                    node.setExtensionElements(extensionElements);
                }
            } else {
                ExtensionElements newInstance = modelInstance.newInstance(ExtensionElements.class);
                //node.setExtensionElements(newInstance);
                CamundaTaskListener camundaTaskListener = newInstance.addExtensionElement(CamundaTaskListener.class);
                camundaTaskListener.setCamundaEvent("create");
                camundaTaskListener.setCamundaClass("com.cmpay.hacp.emergency.service.camunda.listener.UserTaskCreateListener");
                node.setExtensionElements(newInstance);
                CamundaTaskListener completeTaskListener = newInstance.addExtensionElement(CamundaTaskListener.class);
                completeTaskListener.setCamundaEvent("complete");
                completeTaskListener.setCamundaClass("com.cmpay.hacp.emergency.service.camunda.listener.UserTaskCompleteListener");
                node.setExtensionElements(newInstance);
                CamundaTaskListener deleteTaskListener = extensionElements.addExtensionElement(CamundaTaskListener.class);
                deleteTaskListener.setCamundaEvent("delete");
                deleteTaskListener.setCamundaClass("com.cmpay.hacp.emergency.service.camunda.listener.UserTaskDeleteListener");
                node.setExtensionElements(newInstance);
            }
        }
    }

    @Override
    public boolean isNeedEncrypt() {
        return false;
    }

    @Override
    public String getBpmTaskType() {
        return CamundaUtil.CAMUNDA_USER_TASK_ELEMENT;
    }

    @Override
    public String getActivityType() {
        return CamundaUtil.CAMUNDA_USER_TASK;
    }

    @Override
    public boolean checkTaskParam(String json) {
        HacpEmergencyTaskBO taskBO = (HacpEmergencyTaskBO)toTaskParam(json);
        if (taskBO == null) {
            return false;
        }
        return JudgeUtils.isNotBlank(taskBO.getTaskOperator()) ;
    }

    @Override
    public TaskParam toTaskParam(String json) {
        return JsonUtil.strToObject(json, HacpEmergencyTaskBO.class);
    }
}
