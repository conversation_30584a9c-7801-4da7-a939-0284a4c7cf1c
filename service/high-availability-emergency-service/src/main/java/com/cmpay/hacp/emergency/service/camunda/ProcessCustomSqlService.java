package com.cmpay.hacp.emergency.service.camunda;

import com.cmpay.hacp.tenant.entity.ActRuExecutionDO;
import com.cmpay.hacp.tenant.entity.ActRuTaskDO;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * @description: 当Camunda提供的Java api 无法满足需求时，使用自定义sql查询对应的流程表，获取相应的数据
 * <AUTHOR>
 * @date 2024/5/14 17:59
 * @version 1.0
 */
public interface ProcessCustomSqlService {

    List<ActRuExecutionDO> getRuExecutions(String businessKey);

    List<ActRuTaskDO> getRuTasks(String executionId, String actId, String tenantId);

    void updateUserTaskOperation(ActRuTaskDO actRuTaskDO);

    String addRuVariableNoRollback(String executionId, String name, String value, String tenantId, String processDefId) ;

    void updateHiProinstStartTime(String id, Date startDate);

    void updateHiProinstUserId(String id, String userId);

    Set<String> queryHiVarinstTagId(String tagId);
}
