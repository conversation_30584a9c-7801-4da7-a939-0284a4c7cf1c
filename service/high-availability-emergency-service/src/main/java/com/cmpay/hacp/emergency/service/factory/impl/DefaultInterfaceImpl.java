package com.cmpay.hacp.emergency.service.factory.impl;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.cmpay.hacp.emergency.bo.task.HttpRequestBaseBO;
import com.cmpay.hacp.emergency.service.factory.AbstractInterfaceStrategyFactory;
import com.cmpay.hacp.enums.MsgEnum;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import org.springframework.stereotype.Service;

/**
 * @description: 默认调用HTTP接口，报文明文传输，无签名
 * <AUTHOR>
 * @date 2024/5/27 14:27
 * @version 1.0
 */
@Service(value = "defaultInterfaceImpl")
public class DefaultInterfaceImpl extends AbstractInterfaceStrategyFactory {
    @Override
    public String doSign(String content) {
        return null;
    }

    @Override
    public String verifySign(String sign, String cipherTextBody) {
        return null;
    }

    @Override
    public String doCipher(String content) {
        return null;
    }

    @Override
    public String doDecrypt(String cipher) {
        return null;
    }

    @Override
    public HttpResponse request(HttpRequestBaseBO request){
        HttpRequest http = null;
        if (JudgeUtils.equals("GET", request.getType())) {
            http = HttpRequest.get(request.getRequestUrl());
        } else if (JudgeUtils.equals("POST", request.getType())) {
            http = HttpRequest.post(request.getRequestUrl());
        } else {
            BusinessException.throwBusinessException(MsgEnum.PROCESS_API_EXECUTE_FAILED);
        }
        return http.addHeaders(request.getHeaders())
                .cookie(request.getCookies())
                .body(request.getContent())
                .setReadTimeout(10000).setConnectionTimeout(10000)
                .execute();
    }
}
