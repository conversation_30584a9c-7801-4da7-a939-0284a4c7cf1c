package com.cmpay.hacp.emergency.service;

import com.cmpay.hacp.emergency.bo.EmergencyHostBO;
import com.cmpay.hacp.enums.KeyValue;
import com.cmpay.lemon.framework.page.PageInfo;
import com.cmpay.lemon.framework.security.UserInfoBase;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2024/08/21 11:29
 * @since 1.0.0
 */

public interface EmergencyHostService {
    void add(EmergencyHostBO bo);

    void update(EmergencyHostBO bo);

    void delete(EmergencyHostBO bo);

    EmergencyHostBO getDetailInfo(EmergencyHostBO bo);

    PageInfo<EmergencyHostBO> getPage(int pageNum, int pageSize, EmergencyHostBO bo);

    List<EmergencyHostBO> getList(EmergencyHostBO bo);

    Map<String, List<KeyValue>> getDropDown();


    List<EmergencyHostBO> findByIds(List<Integer> ids,String workspaceId);

    List<EmergencyHostBO> findByTagIds(List<Integer> ids,String workspaceId);

    List<EmergencyHostBO> findByAppIds(List<Integer> ids,String workspaceId);

    void template(String workspaceId);

    void importHost(List<EmergencyHostBO> dataList, String workspaceId, UserInfoBase loginUser);
}
