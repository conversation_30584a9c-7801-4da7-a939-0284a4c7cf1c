<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.hacp.emergency.dao.IImportHistoryFileInfoExtDao" >

    <resultMap id="BaseResultMap" type="com.cmpay.hacp.emergency.entity.ImportHistoryFileInfoDO" >
        <id column="id" property="id" jdbcType="INTEGER" />
        <result column="file_name" property="fileName" jdbcType="VARCHAR" />
        <result column="excel_count" property="excelCount" jdbcType="INTEGER" />
        <result column="excel_success" property="excelSuccess" jdbcType="INTEGER" />
        <result column="excel_fail" property="excelFail" jdbcType="INTEGER" />
        <result column="excel_success_remark" property="excelSuccessRemark" jdbcType="VARCHAR" />
        <result column="excel_fail_remark" property="excelFailRemark" jdbcType="VARCHAR" />
        <result column="batch_number" property="batchNumber" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="workspace_id" property="workspaceId" jdbcType="VARCHAR" />
        <result column="operator_id" property="operatorId" jdbcType="VARCHAR" />
        <result column="operator_name" property="operatorName" jdbcType="VARCHAR" />
    </resultMap>

    <resultMap id="ResultMapWithBLOBs" type="com.cmpay.hacp.emergency.entity.ImportHistoryFileInfoDO" extends="BaseResultMap" >
        <result column="file_blob" property="fileBlob" jdbcType="LONGVARBINARY" />
    </resultMap>

    <sql id="Base_Column_List" >
        id, file_name, excel_count, excel_success, excel_fail, excel_success_remark, excel_fail_remark, 
        batch_number, create_time, workspace_id, operator_id, operator_name
    </sql>

    <sql id="Blob_Column_List" >
        file_blob
    </sql>

</mapper>