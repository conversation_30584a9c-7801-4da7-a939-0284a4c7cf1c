<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.hacp.emergency.dao.IEmergencyHostArchiveDao" >

    <resultMap id="BaseResultMap" type="com.cmpay.hacp.emergency.entity.EmergencyHostArchiveDO" >
        <id column="id" property="id" jdbcType="BIGINT" />
        <result column="host_id" property="hostId" jdbcType="BIGINT" />
        <result column="activity_id" property="activityId" jdbcType="VARCHAR" />
        <result column="business_key" property="businessKey" jdbcType="VARCHAR" />
        <result column="workspace_id" property="workspaceId" jdbcType="VARCHAR" />
        <result column="host_desc" property="hostDesc" jdbcType="VARCHAR" />
        <result column="host_address" property="hostAddress" jdbcType="VARCHAR" />
        <result column="host_port" property="hostPort" jdbcType="INTEGER" />
        <result column="host_username" property="hostUsername" jdbcType="VARCHAR" />
        <result column="host_password" property="hostPassword" jdbcType="VARCHAR" />
        <result column="secret_key" property="secretKey" jdbcType="VARCHAR" />
        <result column="host_os" property="hostOS" jdbcType="VARCHAR" />
    </resultMap>

    <sql id="Base_Column_List" >
        id, host_id, activity_id, business_key, workspace_id, host_desc, host_address, host_port, 
        host_username, host_password, secret_key, host_os
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.Long" >
        select 
        <include refid="Base_Column_List" />
        from emergency_host_archive
        where id = #{id,jdbcType=BIGINT}
    </select>

    <delete id="delete" parameterType="java.lang.Long" >
        delete from emergency_host_archive
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <insert id="insert" parameterType="com.cmpay.hacp.emergency.entity.EmergencyHostArchiveDO" >
        insert into emergency_host_archive
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="id != null" >
                id,
            </if>
            <if test="hostId != null" >
                host_id,
            </if>
            <if test="activityId != null" >
                activity_id,
            </if>
            <if test="businessKey != null" >
                business_key,
            </if>
            <if test="workspaceId != null" >
                workspace_id,
            </if>
            <if test="hostDesc != null" >
                host_desc,
            </if>
            <if test="hostAddress != null" >
                host_address,
            </if>
            <if test="hostPort != null" >
                host_port,
            </if>
            <if test="hostUsername != null" >
                host_username,
            </if>
            <if test="hostPassword != null" >
                host_password,
            </if>
            <if test="secretKey != null" >
                secret_key,
            </if>
            <if test="hostOS != null" >
                host_os,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="id != null" >
                #{id,jdbcType=BIGINT},
            </if>
            <if test="hostId != null" >
                #{hostId,jdbcType=BIGINT},
            </if>
            <if test="activityId != null" >
                #{activityId,jdbcType=VARCHAR},
            </if>
            <if test="businessKey != null" >
                #{businessKey,jdbcType=VARCHAR},
            </if>
            <if test="workspaceId != null" >
                #{workspaceId,jdbcType=VARCHAR},
            </if>
            <if test="hostDesc != null" >
                #{hostDesc,jdbcType=VARCHAR},
            </if>
            <if test="hostAddress != null" >
                #{hostAddress,jdbcType=VARCHAR},
            </if>
            <if test="hostPort != null" >
                #{hostPort,jdbcType=INTEGER},
            </if>
            <if test="hostUsername != null" >
                #{hostUsername,jdbcType=VARCHAR},
            </if>
            <if test="hostPassword != null" >
                #{hostPassword,jdbcType=VARCHAR},
            </if>
            <if test="secretKey != null" >
                #{secretKey,jdbcType=VARCHAR},
            </if>
            <if test="hostOS != null" >
                #{hostOS,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.cmpay.hacp.emergency.entity.EmergencyHostArchiveDO" >
        update emergency_host_archive
        <set >
            <if test="hostId != null" >
                host_id = #{hostId,jdbcType=BIGINT},
            </if>
            <if test="activityId != null" >
                activity_id = #{activityId,jdbcType=VARCHAR},
            </if>
            <if test="businessKey != null" >
                business_key = #{businessKey,jdbcType=VARCHAR},
            </if>
            <if test="workspaceId != null" >
                workspace_id = #{workspaceId,jdbcType=VARCHAR},
            </if>
            <if test="hostDesc != null" >
                host_desc = #{hostDesc,jdbcType=VARCHAR},
            </if>
            <if test="hostAddress != null" >
                host_address = #{hostAddress,jdbcType=VARCHAR},
            </if>
            <if test="hostPort != null" >
                host_port = #{hostPort,jdbcType=INTEGER},
            </if>
            <if test="hostUsername != null" >
                host_username = #{hostUsername,jdbcType=VARCHAR},
            </if>
            <if test="hostPassword != null" >
                host_password = #{hostPassword,jdbcType=VARCHAR},
            </if>
            <if test="secretKey != null" >
                secret_key = #{secretKey,jdbcType=VARCHAR},
            </if>
            <if test="hostOS != null" >
                host_os = #{hostOS,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="find" resultMap="BaseResultMap" parameterType="com.cmpay.hacp.emergency.entity.EmergencyHostArchiveDO" >
        select 
        <include refid="Base_Column_List" />
        from emergency_host_archive
        <where >
            <if test="id != null" >
                and id = #{id,jdbcType=BIGINT}
            </if>
            <if test="hostId != null" >
                and host_id = #{hostId,jdbcType=BIGINT}
            </if>
            <if test="activityId != null" >
                and activity_id = #{activityId,jdbcType=VARCHAR}
            </if>
            <if test="businessKey != null" >
                and business_key = #{businessKey,jdbcType=VARCHAR}
            </if>
            <if test="workspaceId != null" >
                and workspace_id = #{workspaceId,jdbcType=VARCHAR}
            </if>
            <if test="hostDesc != null" >
                and host_desc = #{hostDesc,jdbcType=VARCHAR}
            </if>
            <if test="hostAddress != null" >
                and host_address = #{hostAddress,jdbcType=VARCHAR}
            </if>
            <if test="hostPort != null" >
                and host_port = #{hostPort,jdbcType=INTEGER}
            </if>
            <if test="hostUsername != null" >
                and host_username = #{hostUsername,jdbcType=VARCHAR}
            </if>
            <if test="hostPassword != null" >
                and host_password = #{hostPassword,jdbcType=VARCHAR}
            </if>
            <if test="secretKey != null" >
                and secret_key = #{secretKey,jdbcType=VARCHAR}
            </if>
            <if test="hostOS != null" >
                and host_os = #{hostOS,jdbcType=VARCHAR}
            </if>
        </where>
    </select>
</mapper>