<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.hacp.dispatch.dao.IDispatchExtDao" >

    <resultMap id="BaseResultMap" type="com.cmpay.hacp.dispatch.entity.DispatchDO" >
        <id column="dispatch_id" property="dispatchId" jdbcType="VARCHAR" />
        <result column="workspace_id" property="workspaceId" jdbcType="VARCHAR" />
        <result column="dispatch_name" property="dispatchName" jdbcType="VARCHAR" />
        <result column="dispatch_cn" property="dispatchCn" jdbcType="VARCHAR" />
        <result column="dispatch_desc" property="dispatchDesc" jdbcType="VARCHAR" />
        <result column="api_location_id" property="apiLocationId" jdbcType="VARCHAR" />
        <result column="api_tag" property="apiTag" jdbcType="VARCHAR" />
        <result column="api_rule_ids" property="apiRuleIds" jdbcType="VARCHAR" />
        <result column="status" property="status" jdbcType="TINYINT" />
        <result column="is_emergency" property="isEmergency" jdbcType="TINYINT" />
        <result column="priority_level" property="priorityLevel" jdbcType="TINYINT" />
        <result column="operator_id" property="operatorId" jdbcType="VARCHAR" />
        <result column="operator_name" property="operatorName" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    </resultMap>

    <sql id="Base_Column_List" >
        dispatch_id, workspace_id, dispatch_cn, dispatch_name, dispatch_desc, api_location_id,
        api_tag, api_rule_ids, status, is_emergency, priority_level, operator_id, operator_name,
        create_time, update_time
    </sql>

    <select id="findSortedDispatches" resultMap="BaseResultMap" parameterType="com.cmpay.hacp.dispatch.entity.DispatchDO" >
        select 
        <include refid="Base_Column_List" />
        from dispatch
        <where >
            <if test="dispatchId != null" >
                and dispatch_id = #{dispatchId,jdbcType=VARCHAR}
            </if>
            <if test="workspaceId != null" >
                and workspace_id = #{workspaceId,jdbcType=VARCHAR}
            </if>
            <if test="dispatchName != null" >
                and dispatch_name = #{dispatchName,jdbcType=VARCHAR}
            </if>
            <if test="dispatchDesc != null" >
                and dispatch_desc = #{dispatchDesc,jdbcType=VARCHAR}
            </if>
            <if test="apiLocationId != null" >
                and api_location_id = #{apiLocationId,jdbcType=VARCHAR}
            </if>
            <if test="apiTag != null" >
                and api_tag = #{apiTag,jdbcType=VARCHAR}
            </if>
            <if test="apiRuleIds != null" >
                and api_rule_ids = #{apiRuleIds,jdbcType=VARCHAR}
            </if>
            <if test="status != null" >
                and status = #{status,jdbcType=TINYINT}
            </if>
            <if test="isEmergency != null" >
                and is_emergency = #{isEmergency,jdbcType=TINYINT}
            </if>
            <if test="priorityLevel != null" >
                and priority_level = #{priorityLevel,jdbcType=TINYINT}
            </if>
            <if test="operatorId != null" >
                and operator_id = #{operatorId,jdbcType=VARCHAR}
            </if>
            <if test="createTime != null" >
                and create_time = #{createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="updateTime != null" >
                and update_time = #{updateTime,jdbcType=TIMESTAMP}
            </if>
        </where>
        order by is_emergency desc, priority_level asc;
    </select>

    <select id="likeFind" resultMap="BaseResultMap" parameterType="com.cmpay.hacp.dispatch.entity.DispatchDO" >
        select
        dispatch_id, d.workspace_id, dispatch_name, dispatch_desc,dispatch_cn, a.api_location_cn as api_location_id, t.api_tag,
        api_rule_ids, d.status, is_emergency, priority_level, d.operator_id, d.operator_name, d.create_time, d.update_time
        from dispatch d left join api_location a on d.api_location_id=a.api_location_id
        left join api_tag t on d.api_tag=t.api_tag_id
        <where >
            <if test="workspaceId != null" >
                and d.workspace_id = #{workspaceId,jdbcType=VARCHAR}
            </if>
            <if test="dispatchName != null" >
                and dispatch_name = #{dispatchName,jdbcType=VARCHAR}
            </if>
            <if test="dispatchCn != null" >
                and dispatch_cn = #{dispatchCn,jdbcType=VARCHAR}
            </if>
            <if test="dispatchDesc != null" >
                and dispatch_desc = #{dispatchDesc,jdbcType=VARCHAR}
            </if>
            <if test="apiLocationId != null and apiLocationId !=''" >
                and a.api_location_name like concat('%',#{apiLocationId,jdbcType=VARCHAR},'%')
            </if>
            <if test="apiTag != null and apiTag !=''" >
                and t.api_tag like concat('%',#{apiTag,jdbcType=VARCHAR},'%')
            </if>
            <if test="isEmergency != null" >
                and is_emergency = #{isEmergency,jdbcType=TINYINT}
            </if>
            <if test="status != null" >
                and d.status = #{status,jdbcType=TINYINT}
            </if>
            <if test="priorityLevel != null" >
                and priority_level = #{priorityLevel,jdbcType=TINYINT}
            </if>
        </where>
        order by priority_level asc
    </select>
</mapper>