package com.cmpay.hacp.dispatch.service.impl;

import com.cmpay.hacp.dispatch.bo.DispatchBO;
import com.cmpay.hacp.dispatch.bo.DispatchNodeBO;
import com.cmpay.hacp.dispatch.bo.DispatchRuleBO;
import com.cmpay.hacp.dispatch.bo.RuleExpressionBO;
import com.cmpay.hacp.dispatch.client.AgentNodeFeignClient;
import com.cmpay.hacp.dispatch.client.FeignClientCache;
import com.cmpay.hacp.dispatch.dao.IDispatchRuleExtDao;
import com.cmpay.hacp.dispatch.entity.DispatchRuleDO;
import com.cmpay.hacp.dispatch.service.DispatchNodeService;
import com.cmpay.hacp.dispatch.service.DispatchRuleService;
import com.cmpay.hacp.dispatch.service.DispatchService;
import com.cmpay.hacp.dispatch.service.RuleExpressionService;
import com.cmpay.hacp.enums.ByteStatusEnum;
import com.cmpay.hacp.enums.DispatchMsgEnum;
import com.cmpay.hacp.tenant.utils.TenantUtils;
import com.cmpay.hacp.utils.bean.BeanConvertUtil;
import com.cmpay.hafr.agent.domain.report.Dictionary;
import com.cmpay.hafr.agent.dto.DictionaryReport;
import com.cmpay.hafr.agent.util.DictionaryUtils;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.framework.data.DefaultRspDTO;
import com.cmpay.lemon.framework.utils.PageUtils;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
public class DispatchRuleServiceImpl implements DispatchRuleService {

    @Autowired
    private IDispatchRuleExtDao dispatchRuleDao;

    @Autowired
    private RuleExpressionService ruleExpressionService;
    @Autowired
    private DispatchNodeService dispatchNodeService;
    @Autowired
    DispatchService dispatchService;
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void addDispatchRule(DispatchRuleBO dispatchRuleBO) {
        dispatchRuleBO.check();
        if(StringUtils.isNotBlank(dispatchRuleBO.getDispatchRuleName())) {
            existFind(dispatchRuleBO);
        }
        dispatchRuleDao.insert(dispatchRuleBO);
        if(StringUtils.isBlank(dispatchRuleBO.getDispatchRuleName())) {
            dispatchRuleBO.setDispatchRuleName("R"+dispatchRuleBO.getDispatchRuleId());
            dispatchRuleDao.update(dispatchRuleBO);
        }

        ruleExpressionService.addRuleExpressions(dispatchRuleBO.getDispatchRuleId(), dispatchRuleBO.getRuleExpressions());
    }

    private void existFind(DispatchRuleBO dispatchRuleBO) {
        DispatchRuleDO findRuleName = new DispatchRuleDO();
        findRuleName.setDispatchRuleId(dispatchRuleBO.getDispatchRuleId());
        findRuleName.setWorkspaceId(dispatchRuleBO.getWorkspaceId());
        findRuleName.setDispatchRuleName(dispatchRuleBO.getDispatchRuleName());
        List<DispatchRuleDO> dispatchRuleDOS = dispatchRuleDao.existFind(findRuleName);
        if(JudgeUtils.isNotEmpty(dispatchRuleDOS)) {
            BusinessException.throwBusinessException(DispatchMsgEnum.RULE_NAME_EXISTS);
        }
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void deleteDispatchRule(Integer dispatchRuleId) {
        DispatchBO dispatchBO = new DispatchBO();
        dispatchBO.setWorkspaceId(TenantUtils.getWorkspaceId());
        dispatchBO.setStatus(ByteStatusEnum.ENABLE.getValue());
        dispatchBO.setApiRuleIds(dispatchRuleId.intValue() + "");
        if(dispatchService.existBind(dispatchBO)){
            BusinessException.throwBusinessException(DispatchMsgEnum.EXIST_API_LOCATION_BIND_GROUP);
        }
        dispatchRuleDao.delete(dispatchRuleId);
        RuleExpressionBO ruleExpressionBO = new RuleExpressionBO();
        ruleExpressionBO.setDispatchRuleId(dispatchRuleId);
        ruleExpressionService.deleteRuleExpression(ruleExpressionBO);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void updateDispatchRule(DispatchRuleBO dispatchRuleBO) {
        dispatchRuleBO.check();
        existFind(dispatchRuleBO);
        dispatchRuleDao.update(dispatchRuleBO);
        ruleExpressionService.deleteByRuleId(dispatchRuleBO.getDispatchRuleId());
        ruleExpressionService.addRuleExpressions(dispatchRuleBO.getDispatchRuleId(),dispatchRuleBO.getRuleExpressions());

    }

    @Override
    public DispatchRuleBO getDispatchRule(Integer dispatchRuleId) {
        RuleExpressionBO ruleExpressionBO = new RuleExpressionBO();
        ruleExpressionBO.setDispatchRuleId(dispatchRuleId);
        ruleExpressionBO.setStatus(ByteStatusEnum.ENABLE.getValue());
        List<RuleExpressionBO> ruleExpressionList = ruleExpressionService.getRuleExpressionList(ruleExpressionBO);
        if(ruleExpressionList != null) {
            ruleExpressionList.stream().forEach(RuleExpressionBO::convertZoneWeightsToList);
            DictionaryReport ruleDictionary = this.getRuleDictionary(TenantUtils.getWorkspaceId());
            Map<String, String> flowKeySourceMap = ruleDictionary.getOrDefault("flowKeySource", new ArrayList<>()).stream().collect(Collectors.toMap(Dictionary::getKey, Dictionary::getDesc));
            Map<String, String> expressionOperatorMap = ruleDictionary.getOrDefault("expressionOperator", new ArrayList<>()).stream().collect(Collectors.toMap(Dictionary::getKey, Dictionary::getDesc));
            ruleExpressionList.stream().forEach(f->{
                f.setDispatchRuleTypeName(flowKeySourceMap.getOrDefault(f.getDispatchRuleType(),null));
                f.setCalcOperatorName(expressionOperatorMap.getOrDefault(f.getCalcOperator(),null));
            });
        }
        DispatchRuleBO dispatchRuleBO = BeanConvertUtil.convert(dispatchRuleDao.get(dispatchRuleId), DispatchRuleBO.class);

        if(dispatchRuleBO != null) {
            dispatchRuleBO.setRuleExpressions(ruleExpressionList);
        }
        return dispatchRuleBO;
    }

    @Override
    public List<DispatchRuleBO> getDispatchRuleList(String workspaceId) {
        DispatchRuleDO entity = new DispatchRuleDO();
        entity.setWorkspaceId(workspaceId);
        entity.setStatus(ByteStatusEnum.ENABLE.getValue());
        List<DispatchRuleDO> dispatchRuleDOS = dispatchRuleDao.find(entity);
        if(JudgeUtils.isEmpty(dispatchRuleDOS)){
            return new ArrayList<>();
        }
        return BeanConvertUtil.convertList(dispatchRuleDOS, DispatchRuleBO.class);
    }

    @Override
    public Map<Integer,String> getDispatchRuleMap(String workspaceId) {
        List<DispatchRuleBO> dispatchRuleList = this.getDispatchRuleList(workspaceId);
        return dispatchRuleList.stream().collect(Collectors.toMap(DispatchRuleBO::getDispatchRuleId, DispatchRuleBO::getDispatchRuleName));
    }

    @Override
    public DictionaryReport getRuleDictionary(String workspaceId) {
        DispatchNodeBO dispatchNodeBO = new DispatchNodeBO();
        dispatchNodeBO.setWorkspaceId(workspaceId);
        dispatchNodeBO.setStatus(ByteStatusEnum.ENABLE.getValue());
        DispatchNodeBO randDispatchNode = dispatchNodeService.randGetNode(dispatchNodeBO);
        if(JudgeUtils.isNull(randDispatchNode)) {
            BusinessException.throwBusinessException(DispatchMsgEnum.NO_USEFUL_DISPATCH_NODE);
        }
        AgentNodeFeignClient nodeClient = FeignClientCache.getClient(AgentNodeFeignClient.class, randDispatchNode.getNodeCallUrl());
        DefaultRspDTO<DictionaryReport> defaultRspDTO = null;
        try {
            defaultRspDTO = nodeClient.reportDictionary();
        } catch (Exception e){
            log.debug("e:{}", e);
        }

        if(JudgeUtils.isNull(defaultRspDTO) || JudgeUtils.isNotSuccess(defaultRspDTO)) {
            DictionaryReport report = new DictionaryReport();
            report.putAll(DictionaryUtils.getDefault());
            return report;

        }
        return defaultRspDTO.getBody();
    }


    @Override
    public PageInfo<DispatchRuleBO> getDispatchRulePage(int pageNum, int pageSize, DispatchRuleBO dispatchRuleBO) {

        return PageUtils.pageQueryWithCount(pageNum, pageSize,
                () -> BeanConvertUtil.convertList(dispatchRuleDao.likeFind(dispatchRuleBO), DispatchRuleBO.class));
    }
}
