package com.cmpay.hacp.dispatch.service;

import com.cmpay.hacp.dispatch.bo.DispatchPushHistoryBO;
import com.cmpay.lemon.framework.page.PageInfo;

import java.util.List;

public interface DispatchPushHistoryService {

    PageInfo<DispatchPushHistoryBO> getDispatchPushHistoryList(int pageNum, int pageSize, DispatchPushHistoryBO dispatchConfigBO);
    DispatchPushHistoryBO addDispatchPushHistory(DispatchPushHistoryBO dispatchConfigBO);
    void updateDispatchPushHistory(DispatchPushHistoryBO dispatchConfigBO);

     List<DispatchPushHistoryBO> getConfigPublishHistory(DispatchPushHistoryBO dispatchPushHistoryBO);

}