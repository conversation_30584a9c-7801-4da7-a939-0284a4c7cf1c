package com.cmpay.hacp.extend.sso.ticket.auth;

import com.cmpay.hacp.system.auth.HacpWebAdminAuthenticationSuccessHandler;
import com.cmpay.hacp.system.bo.system.DictBO;
import com.cmpay.hacp.system.bo.system.UserLoginBO;
import com.cmpay.hacp.extend.sso.ticket.enums.SsoConstant;
import com.cmpay.hacp.extend.sso.ticket.service.SystemSsoLoginService;
import com.cmpay.hacp.extend.sso.ticket.utils.BeanConvertUtil;
import com.cmpay.hacp.dto.system.LoginHistoryLogDTO;
import com.cmpay.hacp.dto.system.UserLoginRspDTO;
import com.cmpay.hacp.system.service.SystemDictionaryService;
import com.cmpay.lemon.common.codec.CodecException;
import com.cmpay.lemon.common.codec.ObjectDecoder;
import com.cmpay.lemon.common.exception.ErrorMsgCode;
import com.cmpay.lemon.common.exception.LemonException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.common.utils.StringUtils;
import com.cmpay.lemon.framework.security.SimpleUserInfo;
import com.cmpay.lemon.framework.security.UserInfoBase;
import com.cmpay.lemon.framework.security.auth.AbstractGenericMatchableAuthenticationProcessor;
import com.cmpay.lemon.framework.security.auth.AuthenticationRequest;
import com.cmpay.lemon.framework.security.auth.GenericAuthenticationToken;
import com.cmpay.lemon.framework.utils.WebUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.security.core.AuthenticationException;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * @author: lihuiquan
 */
public class LemonWebAdminSsoLoginAuthenticationProcessor extends AbstractGenericMatchableAuthenticationProcessor<GenericAuthenticationToken> {

    public static final String TICKET = "ticket";

    public static final String SERVICE = "service";

    public static final String SSO_SERVER_URL = "ssoServerUrl";

    public static final String SSO_SERVER = "ssoServer";

    private static final String DOMAIN = "domain";

    private static final String COLON = ":";

    private final String applicationName;

    private final ObjectDecoder objectDecoder;

    private final SystemSsoLoginService systemSsoLoginService;

    private final SystemDictionaryService systemDictionaryService;

    private final ApplicationContext applicationContext;

    private final ObjectMapper lemonWebAdminObjectMapper;

    @Value("${hacp.web.admin.scim-iam.sso-server: }")
    private String ssoServerUrl;

    /**
     * "filterProcessesUrl"前缀必须与"lemon.security.authentication.loginPathPrefix"一致
     */
    public LemonWebAdminSsoLoginAuthenticationProcessor(SystemSsoLoginService systemSsoLoginService, SystemDictionaryService systemDictionaryService, String filterProcessesUrl, ObjectDecoder objectDecoder, String applicationName, ApplicationContext applicationContext, ObjectMapper lemonWebAdminObjectMapper) {
        super(filterProcessesUrl);
        this.systemSsoLoginService = systemSsoLoginService;
        this.systemDictionaryService = systemDictionaryService;
        this.applicationContext = applicationContext;
        this.applicationName = applicationName;
        this.objectDecoder = objectDecoder;
        this.lemonWebAdminObjectMapper = lemonWebAdminObjectMapper;
    }

    @Override
    protected UserInfoBase doProcessAuthentication(GenericAuthenticationToken genericAuthenticationToken) throws AuthenticationException {
        AuthenticationRequest authenticationRequest = genericAuthenticationToken.getAuthenticationRequest();
        Map<String, String> authenticationRequestParameters = new HashMap<>();
        try {
            HttpServletRequest httpServletRequest = authenticationRequest.getHttpServletRequest();
            String queryString = httpServletRequest.getQueryString();
            if (JudgeUtils.isBlank(queryString)) {
                authenticationRequestParameters = this.objectDecoder.readValue(AbstractGenericMatchableAuthenticationProcessor.getRequestInputStream(authenticationRequest), Map.class);
            } else {
                this.resolveQueryString(queryString, authenticationRequestParameters);
            }
        } catch (CodecException e) {
            LemonException.throwLemonException(ErrorMsgCode.AUTHENTICATION_FAILURE, e);
        }
        if (JudgeUtils.isEmpty(authenticationRequestParameters)) {
            LemonException.throwLemonException(ErrorMsgCode.AUTHENTICATION_FAILURE, "No authentication parameter found in request body.");
        }
        //获取登录参数
        String ticket = authenticationRequestParameters.get(TICKET);
        String service = authenticationRequestParameters.get(SERVICE);
        if (JudgeUtils.isBlank(service)) {
            service = this.getService(SERVICE, this.applicationName.concat(COLON).concat(this.getActiveProfile()));
        } else {
            String[] arrays = StringUtils.split(service, COLON);
            service = this.getService(DOMAIN, this.applicationName.concat(COLON).concat(arrays[0]));
        }
        UserLoginBO userLoginBO = systemSsoLoginService.ssoLogin(this.getSsoServer(), ticket, service);
        UserLoginRspDTO userInfoDTO = BeanConvertUtil.getUserLoginRspDTO(userLoginBO);
        //session 暂时存入
        HttpSession httpSession = WebUtils.getHttpServletRequest().getSession();
        try {
            String userInfoJson = this.lemonWebAdminObjectMapper.writeValueAsString(userInfoDTO);
            httpSession.setAttribute(HacpWebAdminAuthenticationSuccessHandler.LOGIN_INFO, userInfoJson);
            httpSession.setAttribute(SsoConstant.SCIM_USER_NAME,
                    Optional.ofNullable(userInfoDTO.getLoginHistory()).map(LoginHistoryLogDTO::getName).orElse(null));
        } catch (Exception e) {
            LemonException.throwLemonException(ErrorMsgCode.AUTHENTICATION_FAILURE, e.getMessage());
        }
        SimpleUserInfo simpleUserInfo = new SimpleUserInfo(userInfoDTO.getUserInfo().getUpmsUserId(), userInfoDTO.getUserInfo().getUserName(), userInfoDTO.getUserInfo().getMobile());
        return simpleUserInfo;
    }

    /**
     * 单点登录地址
     *
     * @return
     */
    private String getSsoServer() {
        String ssoServer = null;
        DictBO dictBO = new DictBO();
        dictBO.setType(SSO_SERVER);
        List<DictBO> dictBOS = systemDictionaryService.queryDictInfos(dictBO);
        if (JudgeUtils.isNotEmpty(dictBOS)) {
            for (DictBO d :
                    dictBOS) {
                if (JudgeUtils.equalsIgnoreCase(SSO_SERVER_URL.concat(":").concat(this.getActiveProfile()), d.getLabel())) {
                    ssoServer = d.getValue();
                    break;
                }
            }
        }
        if (JudgeUtils.isBlank(ssoServer)) {
            LemonException.throwLemonException(ErrorMsgCode.AUTHENTICATION_FAILURE, "sso server is null");
        }

        return ssoServer;
    }

    private String getService(String label, String domain) {
        DictBO dictBO = new DictBO();
        dictBO.setLabel(label);
        dictBO.setType(domain);
        List<DictBO> dictBOS = systemDictionaryService.queryDictInfos(dictBO);
        if (JudgeUtils.isEmpty(dictBOS) || dictBOS.size() != 1) {
            if(JudgeUtils.isNotBlank(ssoServerUrl)){
                return ssoServerUrl;
            }
            LemonException.throwLemonException(ErrorMsgCode.AUTHENTICATION_FAILURE, "sso service is null");
        }
        return dictBOS.get(0).getValue();
    }

    private void resolveQueryString(String query, Map<String, String> params) {
        String[] arrays = org.apache.commons.lang3.StringUtils.split(query, '&');
        for (int i = 0, n = arrays.length; i < n; i++) {
            String[] kv = org.apache.commons.lang3.StringUtils.split(arrays[0], '=');
            if (kv.length == 2) {
                params.put(kv[0], kv[1]);
            }
        }
    }

    public String getActiveProfile() {
        return applicationContext.getEnvironment().getActiveProfiles()[0];
    }

}


