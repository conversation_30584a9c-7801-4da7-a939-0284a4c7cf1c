package com.cmpay.hacp.extend.sso.ticket.utils;

import com.cmpay.hacp.system.bo.system.LoginHistoryLogBO;
import com.cmpay.hacp.system.bo.system.UserLoginBO;
import com.cmpay.hacp.dto.system.LoginHistoryLogDTO;
import com.cmpay.hacp.dto.system.UserLoginRspDTO;
import com.cmpay.lemon.common.utils.BeanUtils;
import com.cmpay.lemon.common.utils.DateTimeUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;

/**
 * 复杂对象相互转换
 *
 * <AUTHOR>
 */
public class BeanConvertUtil {

    /**
     * @param userLoginBO
     * @return
     */
    public static UserLoginRspDTO getUserLoginRspDTO(UserLoginBO userLoginBO) {
        //返回登录的信息
        UserLoginRspDTO userInfoDTO = new UserLoginRspDTO();
        BeanUtils.copy(userInfoDTO, userLoginBO);
        LoginHistoryLogDTO loginHistoryLog = new LoginHistoryLogDTO();
        if (JudgeUtils.isNotNull(userLoginBO.getLoginHistory())) {
            BeanUtils.copyProperties(loginHistoryLog, userLoginBO.getLoginHistory());
            userInfoDTO.setLoginHistory(loginHistoryLog);
        }
        userInfoDTO.setUserInfo(userLoginBO.getUserInfo());
        return userInfoDTO;
    }

    /**
     * @param userLoginRspDTO
     * @return
     */
    public static UserLoginBO getUserLoginBO(UserLoginRspDTO userLoginRspDTO) {
        //返回登录的信息
        UserLoginBO userLoginBO = new UserLoginBO();
        userLoginBO.setPwdNeedToModify(userLoginRspDTO.getPwdNeedToModify());
        userLoginBO.setUserInfo(userLoginRspDTO.getUserInfo());
        LoginHistoryLogBO loginHistoryLog = new LoginHistoryLogBO();
        if (JudgeUtils.isNotNull(userLoginRspDTO.getLoginHistory())) {
            BeanUtils.copyProperties(loginHistoryLog, userLoginRspDTO.getLoginHistory());
            userLoginBO.setLoginHistory(loginHistoryLog);
        }
        //上次登录时间
        if (JudgeUtils.isNotBlank(userLoginRspDTO.getLastLoginTime())) {
            //首先取权限中心的
            userLoginBO.setLastLoginTime(userLoginRspDTO.getLastLoginTime());
        }
        if (JudgeUtils.isBlank(userLoginRspDTO.getLastLoginTime())) {
            //其次取当前系统
            if (JudgeUtils.isNotNull(loginHistoryLog)) {
                userLoginBO.setLastLoginTime(DateTimeUtils.formatLocalDateTime(loginHistoryLog.getLoginTime()));
            } else {
                //取当前时间
                userLoginBO.setLastLoginTime(DateTimeUtils.getCurrentDateTimeStr());
            }
        }
        return userLoginBO;
    }

}
