package com.cmpay.hacp.extend.sso.ticket.autoconfigure.ticket;


import com.cmpay.hacp.extend.sso.ticket.properties.LemonWebAdminScimSsoTicketProperties;
import com.cmpay.hacp.extend.sso.ticket.service.ScimTicketService;
import com.cmpay.hacp.extend.sso.ticket.service.impl.ScimTicketServiceImpl;
import com.cmpay.hacp.utils.HttpClientUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.impl.client.CloseableHttpClient;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cloud.openfeign.FeignAutoConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.web.client.RestTemplate;

/**
 * <AUTHOR> 新4A单点登录
 */
@Slf4j
@Configuration(proxyBeanMethods = false)
@ConditionalOnClass({LemonWebAdminScimSsoTicketProperties.class})
@EnableConfigurationProperties(LemonWebAdminScimSsoTicketProperties.class)
@AutoConfigureAfter({FeignAutoConfiguration.class})
public class LemonWebAdminScimSsoTicketAutoConfiguration {

    /**
     * @param lemonWebAdminScimSsoTicketProperties
     * @return scimIamRestTemplate
     */
    @Bean(name = "scimIamRestTemplate")
    @ConditionalOnClass({HttpClientUtil.class, CloseableHttpClient.class, RestTemplate.class, LemonWebAdminScimSsoTicketProperties.class})
    @ConditionalOnBean({CloseableHttpClient.class})
    public RestTemplate scimIamRestTemplate(CloseableHttpClient httpClient, LemonWebAdminScimSsoTicketProperties lemonWebAdminScimSsoTicketProperties) {
        log.info("init scimIamRestTemplate");
        return HttpClientUtil.getInstance(lemonWebAdminScimSsoTicketProperties.getServer().getConnectTimeout(),
                lemonWebAdminScimSsoTicketProperties.getServer().getReadTimeout(), httpClient);
    }

    @Bean
    @Primary
    @ConditionalOnBean({RestTemplate.class})
    @ConditionalOnClass({ScimTicketService.class, RestTemplate.class, ScimTicketServiceImpl.class})
    public ScimTicketService scimTicketService(@Qualifier("scimIamRestTemplate") RestTemplate scimIamRestTemplate, LemonWebAdminScimSsoTicketProperties lemonWebAdminScimSsoTicketProperties) {
        return new ScimTicketServiceImpl(scimIamRestTemplate, lemonWebAdminScimSsoTicketProperties);
    }
}
