package com.cmpay.hacp.dashboard;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.HashMap;
import java.util.Map;

@Data
@ConfigurationProperties(prefix = "hacp.web.dashboard")
public class DashboardProperties {
    private Grafana grafana = new Grafana();

    @Data
    public static class Grafana {
        private String dashboardUrlHttp = "http://127.0.0.1:3000/";
        private String dashboardUrlHttps = "https://127.0.0.1:3443/";
        private String apiUrl = "http://127.0.0.1:3000/api";
        private String username = "admin";
        private String password = "admin";
        private String datasourceName = "hafr-prometheus";
        private Map<String, String> dashboardOptions = new HashMap<>();
    }
}
