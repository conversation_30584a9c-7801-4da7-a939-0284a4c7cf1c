package com.cmpay.hacp.tenant.service.impl;


import com.cmpay.hacp.tenant.bo.TenantWorkspaceBO;
import com.cmpay.hacp.tenant.dao.ITenantWorkspaceExtDao;
import com.cmpay.hacp.tenant.entity.TenantWorkspaceDO;
import com.cmpay.hacp.enums.MsgEnum;
import com.cmpay.hacp.tenant.service.TenantWorkspaceService;
import com.cmpay.hacp.tenant.service.HacpBaseService;
import com.cmpay.hacp.utils.IdGenUtil;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.BeanUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 租户项目管理
 *
 * <AUTHOR>
 */
@Service
public class TenantWorkspaceServiceImpl implements TenantWorkspaceService {

    @Resource
    private ITenantWorkspaceExtDao tenantWorkspaceExtDao;

    @Resource
    private HacpBaseService hacpBaseService;

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = RuntimeException.class)
    public void addTenantWorkspace(String loginName, LocalDateTime localDateTime, String tenantId, String workspaceId) {
        hacpBaseService.workspaceExist(workspaceId);
        hacpBaseService.tenantExist(tenantId);
        TenantWorkspaceDO tenantWorkspaceEntity = new TenantWorkspaceDO();
        tenantWorkspaceEntity.setId(IdGenUtil.generateTenantWorkspaceId());
        tenantWorkspaceEntity.setTenantId(tenantId);
        tenantWorkspaceEntity.setWorkspaceId(workspaceId);
        tenantWorkspaceEntity.setCreateUser(loginName);
        tenantWorkspaceEntity.setCreateTime(localDateTime);
        tenantWorkspaceEntity.setUpdateUser(loginName);
        tenantWorkspaceEntity.setUpdateTime(localDateTime);
        int insertTenantWorkspace = tenantWorkspaceExtDao.insert(tenantWorkspaceEntity);
        if (insertTenantWorkspace < 1) {
            BusinessException.throwBusinessException(MsgEnum.FAIL);
        }
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = RuntimeException.class)
    public void updateTenantWorkspace(String loginName, LocalDateTime localDateTime, String id, String tenantId, String workspaceId) {
        hacpBaseService.workspaceExist(workspaceId);
        hacpBaseService.tenantExist(tenantId);
        TenantWorkspaceDO tenantWorkspaceEntity = new TenantWorkspaceDO();
        tenantWorkspaceEntity.setId(id);
        tenantWorkspaceEntity.setTenantId(tenantId);
        tenantWorkspaceEntity.setWorkspaceId(workspaceId);
        tenantWorkspaceEntity.setUpdateUser(loginName);
        tenantWorkspaceEntity.setUpdateTime(localDateTime);
        int updateTenantWorkspace = tenantWorkspaceExtDao.update(tenantWorkspaceEntity);
        if (updateTenantWorkspace < 1) {
            BusinessException.throwBusinessException(MsgEnum.FAIL);
        }
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = RuntimeException.class)
    public void deleteTenantWorkspaceByTenantId(String tenantId) {
        tenantWorkspaceExtDao.deleteTenantWorkspaceByTenantId(tenantId);
    }

    @Override
    public List<String> getWorkspaceIdsByTenantId(String tenantId) {
        return tenantWorkspaceExtDao.getWorkspaceIdsByTenantId(tenantId);
    }

    @Override
    public List<TenantWorkspaceBO> getWorkspacesByTenantId(String tenantId) {
        return tenantWorkspaceExtDao.getWorkspacesByTenantId(tenantId);
    }


    @Override
    public TenantWorkspaceBO getWorkspaceInfo(String workspaceId) {
        TenantWorkspaceDO tenantWorkspace = tenantWorkspaceExtDao.getTenantWorkspaceByWorkspaceId(workspaceId);
        if (JudgeUtils.isNull(tenantWorkspace)) {
            return null;
        }
        TenantWorkspaceBO result = new TenantWorkspaceBO();
        BeanUtils.copyProperties(result, tenantWorkspace);
        return result;
    }
}
