package com.cmpay.hacp.tenant.service.impl;

import com.cmpay.hacp.tenant.bo.TenantWorkspaceBO;
import com.cmpay.hacp.tenant.bo.TenantWorkspaceUserBO;
import com.cmpay.hacp.tenant.dao.ITenantWorkspaceExtDao;
import com.cmpay.hacp.tenant.dao.IWorkspaceExtDao;
import com.cmpay.hacp.tenant.entity.TenantWorkspaceDO;
import com.cmpay.hacp.tenant.entity.WorkspaceDO;
import com.cmpay.hacp.enums.MsgEnum;
import com.cmpay.hacp.enums.WorkspaceUserTypeEnum;
import com.cmpay.hacp.tenant.service.*;
import com.cmpay.hacp.utils.IdGenUtil;
import com.cmpay.hacp.utils.bean.BeanConvertUtil;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.BeanUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.framework.page.PageInfo;
import com.cmpay.lemon.framework.utils.PageUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 项目管理
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class WorkspaceServiceImpl implements WorkspaceService {

    @Resource
    private IWorkspaceExtDao workspaceExtDao;

    @Resource
    private ITenantWorkspaceExtDao tenantWorkspaceExtDao;

    @Resource
    private TenantWorkspaceService tenantWorkspaceService;

    @Resource
    private TenantWorkspaceUserService tenantWorkspaceUserService;

    @Resource
    private TenantWorkspaceRoleService tenantWorkspaceRoleService;

    @Resource
    private HacpBaseService hacpBaseService;

    @Autowired(required = false)
    private List<WorkspaceBaseService> workspaceBaseServiceList;

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = RuntimeException.class)
    public void addWorkspace(String loginName, TenantWorkspaceBO tenantWorkspace) {
        hacpBaseService.tenantExist(tenantWorkspace.getTenantId());
        //判断项目名称是否重名
        List<TenantWorkspaceBO> tenantWorkspaces = this.getWorkspaceByWorkspaceName(tenantWorkspace.getTenantId(),
                tenantWorkspace.getWorkspaceName());
        if (JudgeUtils.isNotEmpty(tenantWorkspaces)) {
            BusinessException.throwBusinessException(MsgEnum.EXIST_WORKPLACE_NAME);
        }
        //新增项目
        LocalDateTime localDateTime = LocalDateTime.now();
        String workspaceId = IdGenUtil.generateWorkspaceId();
        WorkspaceDO workspaceEntity = new WorkspaceDO();
        BeanUtils.copyProperties(workspaceEntity, tenantWorkspace);
        workspaceEntity.setWorkspaceId(workspaceId);
        workspaceEntity.setCreateUser(loginName);
        workspaceEntity.setCreateTime(localDateTime);
        workspaceEntity.setUpdateUser(loginName);
        workspaceEntity.setUpdateTime(localDateTime);
        int insertWorkspace = workspaceExtDao.insert(workspaceEntity);
        if (insertWorkspace < 1) {
            BusinessException.throwBusinessException(MsgEnum.FAIL);
        }
        //初始化项目角色
        tenantWorkspaceRoleService.initWorkspaceRole(loginName, workspaceId);
        //新增项目管理员
        TenantWorkspaceUserBO tenantWorkspaceUser = new TenantWorkspaceUserBO();
        tenantWorkspaceUser.setWorkspaceId(workspaceId);
        tenantWorkspaceUser.setUserId(tenantWorkspace.getUserId());
        tenantWorkspaceUser.setWorkspaceUserType(WorkspaceUserTypeEnum.ADMIN.getCode());
        tenantWorkspaceUserService.addWorkspaceUser(loginName, tenantWorkspaceUser);
        //关联租户与项目
        tenantWorkspaceService.addTenantWorkspace(loginName, localDateTime, tenantWorkspace.getTenantId(), workspaceId);
    }

    @Override
    public List<TenantWorkspaceBO> getWorkspaceOwnList(TenantWorkspaceBO tenantWorkspaceBO) {
        return workspaceExtDao.getWorkspaceOwnList(tenantWorkspaceBO);
    }

    @Override
    public List<String> getAllWorkspaceIds() {
        return workspaceExtDao.getAllWorkspaces();
    }

    @Override
    public List<TenantWorkspaceBO> getIndexOwnWorkspaces(String userId) {
        List<TenantWorkspaceBO> ownWorkspaces = workspaceExtDao.getIndexOwnWorkspaces(userId);
        if (JudgeUtils.isNotEmpty(ownWorkspaces)) {
            ownWorkspaces.stream().forEach(workspace -> {
                workspace.setWorkspaceRoles(tenantWorkspaceRoleService.getWorkspaceRolesByUserId(workspace.getWorkspaceId(), userId));
            });
        }
        return ownWorkspaces;
    }

    @Override
    public TenantWorkspaceBO getDetailWorkspaceInfo(String workspaceId) {
        return workspaceExtDao.getDetailWorkspaceInfo(workspaceId);
    }

    @Override
    public TenantWorkspaceBO getWorkspaceInfo(String workspaceId) {
        TenantWorkspaceDO tenantWorkspace = tenantWorkspaceExtDao.getTenantWorkspaceByWorkspaceId(workspaceId);
        if (JudgeUtils.isNull(tenantWorkspace)) {
            return null;
        }
        TenantWorkspaceBO riseWorkspace = new TenantWorkspaceBO();
        BeanUtils.copyProperties(riseWorkspace, tenantWorkspace);
        return riseWorkspace;
    }


    @Override
    public PageInfo<TenantWorkspaceBO> getWorkspaceListByPage(int pageNum, int pageSize, TenantWorkspaceBO tenantWorkspace) {
        return PageUtils.pageQueryWithCount(pageNum, pageSize, () -> workspaceExtDao.getWorkspaceAdminList(tenantWorkspace));
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = RuntimeException.class)
    public void deleteWorkspaceByTenantId(String loginName, LocalDateTime localDateTime, String tenantId) {
        List<String> workspaceIds = tenantWorkspaceService.getWorkspaceIdsByTenantId(tenantId);
        if (JudgeUtils.isNotEmpty(workspaceIds)) {
            //删除关联关系
            tenantWorkspaceService.deleteTenantWorkspaceByTenantId(tenantId);
            //循环删除项目
            workspaceIds.forEach(workspaceId -> {
                this.deleteWorkspace(loginName, localDateTime, workspaceId);
            });
        }
    }

    @Override
    public List<TenantWorkspaceBO> getWorkspaceAdminList(TenantWorkspaceBO tenantWorkspace) {
        return workspaceExtDao.getWorkspaceAdminList(tenantWorkspace);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = RuntimeException.class)
    public void updateWorkspace(String loginName, TenantWorkspaceBO tenantWorkspace) {
        hacpBaseService.tenantExist(tenantWorkspace.getTenantId());
        //判断是否重名
        List<TenantWorkspaceBO> tenantWorkspaces = this.getWorkspaceByWorkspaceName(tenantWorkspace.getTenantId(),
                tenantWorkspace.getWorkspaceName());
        if (JudgeUtils.isNotEmpty(tenantWorkspaces)) {
            tenantWorkspaces.stream().forEach(
                    workspace -> {
                        if (!JudgeUtils.equalsIgnoreCase(workspace.getWorkspaceId(), tenantWorkspace.getWorkspaceId())) {
                            BusinessException.throwBusinessException(MsgEnum.EXIST_WORKPLACE_NAME);
                        }
                    }
            );
        }
        LocalDateTime localDateTime = LocalDateTime.now();
        WorkspaceDO workspaceEntity = new WorkspaceDO();
        BeanUtils.copyProperties(workspaceEntity, tenantWorkspace);
        workspaceEntity.setUpdateUser(loginName);
        workspaceEntity.setUpdateTime(localDateTime);
        int updateWorkspace = workspaceExtDao.update(workspaceEntity);
        if (updateWorkspace < 1) {
            BusinessException.throwBusinessException(MsgEnum.FAIL);
        }
        //查询当前用户是否是项目成员
        TenantWorkspaceUserBO tenantWorkspaceUserInfo = tenantWorkspaceUserService.getTenantWorkspaceUserInfo(tenantWorkspace.getWorkspaceId(),
                tenantWorkspace.getUserId());
        if (JudgeUtils.isNull(tenantWorkspaceUserInfo)) {
            //不是，新增用户
            TenantWorkspaceUserBO tenantWorkspaceUser = new TenantWorkspaceUserBO();
            tenantWorkspaceUser.setWorkspaceId(tenantWorkspace.getWorkspaceId());
            tenantWorkspaceUser.setUserId(tenantWorkspace.getUserId());
            tenantWorkspaceUser.setWorkspaceUserType(WorkspaceUserTypeEnum.ADMIN.getCode());
            tenantWorkspaceUserService.addWorkspaceUser(loginName, tenantWorkspaceUser);
        } else {
            //如果不是项目管理员
            if (!JudgeUtils.equalsIgnoreCase(tenantWorkspaceUserInfo.getWorkspaceUserType(), WorkspaceUserTypeEnum.ADMIN.getCode())) {
                //将之前的管理员置为普通用户
                tenantWorkspaceUserService.updateAdminToAnyoneWorkspaceUserByWorkspaceId(loginName,
                        localDateTime,
                        tenantWorkspace.getWorkspaceId());
                //将当前用户置为管理员
                tenantWorkspaceUserService.updateAdminWorkspaceUserByWorkspaceId(loginName,
                        localDateTime,
                        tenantWorkspace.getWorkspaceId(),
                        tenantWorkspace.getUserId());
            }
            //是项目管理员什么都不用做
        }
        //修改绑定关系
        tenantWorkspaceService.updateTenantWorkspace(loginName,
                localDateTime,
                tenantWorkspace.getId(),
                tenantWorkspace.getTenantId(),
                tenantWorkspace.getWorkspaceId());
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = RuntimeException.class)
    public void deleteWorkspace(String loginName, LocalDateTime localDateTime, String workspaceId) {
        if(JudgeUtils.isNotEmpty(workspaceBaseServiceList)){
            workspaceBaseServiceList.forEach(workspaceBaseService->{
                boolean deleted = workspaceBaseService.whetherOrNotToAllowItemsToBeDeleted(workspaceId);
                if(!deleted){
                    log.error("Workspace delete error,class is :{}", workspaceBaseService.getClass().getName());
                    BusinessException.throwBusinessException(MsgEnum.DELETE_WORKPLACE_CLEAR_CONFIG_ERROR);
                }
            });
        }
        //删除项目
        workspaceExtDao.delete(workspaceId);
        //删除项目下的角色
        tenantWorkspaceRoleService.deleteWorkspaceRoleByWorkspaceId(workspaceId);
        //删除项目下的成员
        tenantWorkspaceUserService.deleteWorkspaceUserByWorkspaceId(workspaceId);

    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = RuntimeException.class)
    public void deleteWorkspaces(String loginName, LocalDateTime localDateTime, List<String> workspaceIds) {
        workspaceIds.stream().forEach(workspaceId -> {
            this.deleteWorkspace(loginName, localDateTime, workspaceId);
        });
    }

    @Override
    public List<TenantWorkspaceBO> getWorkspaceByWorkspaceName(String tenantId, String workspaceName) {
        List<WorkspaceDO> workspaceEntities = workspaceExtDao.getWorkspaceByWorkspaceName(tenantId, workspaceName);
        if (JudgeUtils.isEmpty(workspaceEntities)) {
            return new ArrayList<>();
        }
        return BeanConvertUtil.convertList(workspaceEntities, TenantWorkspaceBO.class);
    }
}
