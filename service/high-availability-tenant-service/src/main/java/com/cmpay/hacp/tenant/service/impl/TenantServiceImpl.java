package com.cmpay.hacp.tenant.service.impl;

import com.cmpay.hacp.tenant.bo.TenantBO;
import com.cmpay.hacp.tenant.bo.TenantUserBO;
import com.cmpay.hacp.tenant.bo.TenantWorkspaceBO;
import com.cmpay.hacp.tenant.dao.ITenantExtDao;
import com.cmpay.hacp.tenant.dao.ITenantUserExtDao;
import com.cmpay.hacp.tenant.entity.TenantDO;
import com.cmpay.hacp.tenant.entity.TenantUserDO;
import com.cmpay.hacp.enums.MsgEnum;
import com.cmpay.hacp.enums.TenantUserTypeEnum;
import com.cmpay.hacp.tenant.service.*;
import com.cmpay.hacp.utils.IdGenUtil;
import com.cmpay.hacp.utils.bean.BeanConvertUtil;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.BeanUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.framework.page.PageInfo;
import com.cmpay.lemon.framework.utils.PageUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 租户管理
 *
 * <AUTHOR>
 */
@Service
public class TenantServiceImpl implements TenantService {

    @Resource
    private HacpBaseService hacpBaseService;

    @Autowired
    private ITenantExtDao tenantExtDao;

    @Resource
    private ITenantUserExtDao tenantUserExtDao;

    @Resource
    private TenantWorkspaceService tenantWorkspaceService;

    @Resource
    private WorkspaceService workspaceService;

    @Resource
    private TenantWorkspaceRoleService tenantWorkspaceRoleService;

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = RuntimeException.class)
    public void addTenant(String loginName, TenantBO tenant) {

        //判断租户名称是否重名
        List<TenantBO> tenants = this.getTenantByTenantName(tenant.getTenantName());
        if (JudgeUtils.isNotEmpty(tenants)) {
            BusinessException.throwBusinessException(MsgEnum.EXIST_TENANT_NAME);
        }
        //新增租户
        LocalDateTime localDateTime = LocalDateTime.now();
        String tenantId = IdGenUtil.generateTenantId();
        TenantDO tenantEntity = new TenantDO();
        BeanUtils.copyProperties(tenantEntity, tenant);
        tenantEntity.setTenantId(tenantId);
        tenantEntity.setCreateUser(loginName);
        tenantEntity.setCreateTime(localDateTime);
        tenantEntity.setUpdateUser(loginName);
        tenantEntity.setUpdateTime(localDateTime);
        int insertTenant = tenantExtDao.insert(tenantEntity);
        if (insertTenant < 1) {
            BusinessException.throwBusinessException(MsgEnum.FAIL);
        }
        //删除租户管理员
        this.deleteAdminUserByTenantId(tenantId);
        //新增租户管理员
        this.addTenantUser(loginName, localDateTime, tenant.getUserId(), tenantId, TenantUserTypeEnum.ADMIN.getCode());
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = RuntimeException.class)
    public void bindAdminTenantRole(String userId) {

        Long roleId = tenantWorkspaceRoleService.getRoleIdByRoleName(TenantWorkspaceRoleService.adminWorkspaceRoleName);
        if (JudgeUtils.isNotNull(roleId)) {
            tenantWorkspaceRoleService.deleteSystemUserRole(roleId, userId);
            tenantWorkspaceRoleService.insertSystemUserRole(roleId, userId);
        }
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = RuntimeException.class)
    public void addTenantUser(String loginName, LocalDateTime localDateTime, String userId, String tenantId, String tenantUserType) {
        hacpBaseService.userExist(userId);
        TenantUserDO tenantUserEntity = new TenantUserDO();
        tenantUserEntity.setId(IdGenUtil.generateTenantUserId());
        tenantUserEntity.setTenantId(tenantId);
        tenantUserEntity.setUserId(userId);
        tenantUserEntity.setTenantUserType(tenantUserType);
        tenantUserEntity.setCreateUser(loginName);
        tenantUserEntity.setCreateTime(localDateTime);
        tenantUserEntity.setUpdateUser(loginName);
        tenantUserEntity.setUpdateTime(localDateTime);
        int insertTenantUser = tenantUserExtDao.insert(tenantUserEntity);
        if (insertTenantUser < 1) {
            BusinessException.throwBusinessException(MsgEnum.FAIL);
        }
        if (JudgeUtils.equalsIgnoreCase(tenantUserType, TenantUserTypeEnum.ADMIN.getCode())) {
            this.bindAdminTenantRole(userId);
        }
    }

    @Override
    public List<TenantUserBO> getTenantUsers(String tenantId) {
        return tenantUserExtDao.getTenantUsers(tenantId);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = RuntimeException.class)
    public void deleteAdminUserByTenantId(String tenantId) {
        tenantUserExtDao.deleteAdminTenantUserByTenantId(tenantId);
    }

    @Override
    public List<TenantBO> getDetailTenants(TenantBO tenant) {
        TenantDO tenantEntity = new TenantDO();
        BeanUtils.copyProperties(tenantEntity, tenant);
        return tenantExtDao.getDetailTenants(tenantEntity);
    }

    @Override
    public TenantBO getDetailTenantInfo(String tenantId) {
        TenantBO tenantInfo = tenantExtDao.getDetailTenantInfo(tenantId);
        if (JudgeUtils.isNotNull(tenantInfo)) {
            List<TenantWorkspaceBO> workspaces = tenantWorkspaceService.getWorkspacesByTenantId(tenantId);
            tenantInfo.setWorkspaces(workspaces);
        }
        return tenantInfo;
    }

    @Override
    public PageInfo<TenantBO> getTenantListByPage(int pageNum, int pageSize, TenantBO tenant) {
        TenantDO tenantEntity = new TenantDO();
        BeanUtils.copyProperties(tenantEntity, tenant);
        return PageUtils.pageQueryWithCount(pageNum, pageSize, () -> tenantExtDao.getDetailTenants(tenantEntity));
    }

    @Override
    public List<TenantBO> getOwnTenantList(String userId) {
        return tenantExtDao.getOwnTenantList(userId);
    }

    @Override
    public List<TenantBO> getAdminTenantList(String userId) {
        return tenantExtDao.getAdminTenantList(userId);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = RuntimeException.class)
    public void updateTenant(String loginName, TenantBO tenantBO) {
        List<TenantBO> tenants = this.getTenantByTenantName(tenantBO.getTenantName());
        if (JudgeUtils.isNotEmpty(tenants)) {
            tenants.stream().forEach(
                    tenant -> {
                        if (!JudgeUtils.equalsIgnoreCase(tenant.getTenantId(), tenantBO.getTenantId())) {
                            BusinessException.throwBusinessException(MsgEnum.EXIST_TENANT_NAME);
                        }
                    }
            );
        }
        LocalDateTime localDateTime = LocalDateTime.now();
        TenantDO tenantEntity = new TenantDO();
        BeanUtils.copyProperties(tenantEntity, tenantBO);
        tenantEntity.setUpdateUser(loginName);
        tenantEntity.setUpdateTime(localDateTime);
        int updateTenant = tenantExtDao.update(tenantEntity);
        if (updateTenant < 1) {
            BusinessException.throwBusinessException(MsgEnum.FAIL);
        }
        //删除租户管理员
        this.deleteAdminUserByTenantId(tenantBO.getTenantId());
        //新增租户管理员
        this.addTenantUser(loginName, localDateTime, tenantBO.getUserId(), tenantBO.getTenantId(), TenantUserTypeEnum.ADMIN.getCode());

    }

    @Override
    public boolean isAdminTenantUser(String tenantId, String userId) {
        TenantUserDO tenantUserEntity = new TenantUserDO();
        tenantUserEntity.setTenantId(tenantId);
        tenantUserEntity.setUserId(userId);
        tenantUserEntity.setTenantUserType(TenantUserTypeEnum.ADMIN.getCode());
        return JudgeUtils.isNotEmpty(tenantUserExtDao.find(tenantUserEntity));
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = RuntimeException.class)
    public void deleteTenant(String loginName, LocalDateTime localDateTime, String tenantId) {
        //删除租户
        tenantExtDao.delete(tenantId);
        //删除租户成员
        tenantUserExtDao.deleteTenantUserByTenantId(tenantId);
        //删除租户下的项目
        workspaceService.deleteWorkspaceByTenantId(loginName, localDateTime, tenantId);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = RuntimeException.class)
    public void deleteTenants(String loginName, LocalDateTime localDateTime, List<String> tenantIds) {
        tenantIds.forEach(tenantId -> {
            this.deleteTenant(loginName, localDateTime, tenantId);
        });
    }

    @Override
    public List<TenantBO> getTenantByTenantName(String tenantName) {
        TenantDO tenantEntity = new TenantDO();
        tenantEntity.setTenantName(tenantName);
        List<TenantDO> tenantEntities = tenantExtDao.find(tenantEntity);
        if (JudgeUtils.isEmpty(tenantEntities)) {
            return new ArrayList<>();
        }
        return BeanConvertUtil.convertList(tenantEntities, TenantBO.class);
    }
}
