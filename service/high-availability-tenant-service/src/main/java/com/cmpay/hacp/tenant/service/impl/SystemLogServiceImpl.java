package com.cmpay.hacp.tenant.service.impl;

import com.cmpay.hacp.system.log.service.adapter.SystemStaticLogAdapter;
import com.cmpay.hacp.tenant.bo.TenantWorkspaceBO;
import com.cmpay.hacp.system.bo.system.SystemLogBO;
import com.cmpay.hacp.system.dao.ISystemLogExtDao;
import com.cmpay.hacp.system.entity.SystemLogDO;
import com.cmpay.hacp.system.log.bo.SysStaticLogBO;
import com.cmpay.hacp.tenant.service.WorkspaceService;
import com.cmpay.hacp.utils.IdGenUtil;
import com.cmpay.hacp.tenant.utils.TenantUtils;
import com.cmpay.lemon.common.utils.BeanUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.framework.page.PageInfo;
import com.cmpay.lemon.framework.utils.PageUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * 系统日志管理
 */
@Service
@Primary
@Slf4j
public class SystemLogServiceImpl extends SystemStaticLogAdapter {

    @Resource
    @Lazy
    private ISystemLogExtDao riseSystemLogExtDao;

    @Resource
    private WorkspaceService workspaceService;

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void add(SysStaticLogBO sysStaticLogBO) {
        try {
            SystemLogBO riseSystemLog = new SystemLogBO();
            BeanUtils.copyProperties(riseSystemLog, sysStaticLogBO);
            if (JudgeUtils.isBlank(riseSystemLog.getWorkspaceId())) {
                Optional.ofNullable(TenantUtils.getWorkspaceId()).ifPresent(riseSystemLog::setWorkspaceId);
            }
            if (JudgeUtils.isNotBlank(riseSystemLog.getWorkspaceId())) {
                TenantWorkspaceBO workspaceInfo = workspaceService.getWorkspaceInfo(riseSystemLog.getWorkspaceId());
                if (JudgeUtils.isNotNull(workspaceInfo) && JudgeUtils.isNotBlank(workspaceInfo.getTenantId())) {
                    riseSystemLog.setTenantId(workspaceInfo.getTenantId());
                }
            }
            SystemLogDO riseSystemLogEntity = new SystemLogDO();
            BeanUtils.copyProperties(riseSystemLogEntity, riseSystemLog);
            if (JudgeUtils.isNotBlank(riseSystemLog.getLogId())) {
                riseSystemLogEntity.setRequestId(riseSystemLog.getLogId());
            }
            riseSystemLogEntity.setId(IdGenUtil.generatorLogId());
            riseSystemLogExtDao.insert(riseSystemLogEntity);
        } catch (Exception e) {
            log.warn("RiseSystemLogServiceImpl add Exception {}", e.getMessage());
        }
    }

    @Override
    public PageInfo list(int pageNum, int pageSize, SysStaticLogBO sysStaticLogBO) {
        SystemLogBO systemLog = new SystemLogBO();
        BeanUtils.copyProperties(systemLog, sysStaticLogBO);
        PageInfo<SystemLogBO> pageInfo = PageUtils.pageQueryWithCount(pageNum, pageSize, () -> riseSystemLogExtDao.getSystemLogs(systemLog));
        if (JudgeUtils.isNotEmpty(pageInfo.getList())) {
            pageInfo.getList().forEach(systemLogInfo -> {
                if (JudgeUtils.isNotNull(systemLogInfo)) {
                    systemLogInfo.setIsDynamic(JudgeUtils.isNotEmpty(systemLogInfo.getDynamicLogs()) ? 1 : 0);
                }
            });
        }
        return pageInfo;
    }

    @Override
    public SysStaticLogBO info(String id) {
        SystemLogBO systemLogInfo = riseSystemLogExtDao.getSystemLogInfo(id);
        if (JudgeUtils.isNotNull(systemLogInfo)) {
            systemLogInfo.setIsDynamic(JudgeUtils.isNotEmpty(systemLogInfo.getDynamicLogs()) ? 1 : 0);
        }
        return systemLogInfo;
    }

    @Override
    public void delete(List<String> ids) {
        riseSystemLogExtDao.deleteByIds(ids);
    }


}
