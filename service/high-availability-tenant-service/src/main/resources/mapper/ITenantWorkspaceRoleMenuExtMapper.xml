<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.hacp.tenant.dao.ITenantWorkspaceRoleMenuExtDao">
    <resultMap id="BaseResultMap" type="com.cmpay.hacp.tenant.entity.TenantWorkspaceRoleMenuDO">
        <id column="id" property="id" jdbcType="VARCHAR"/>
        <result column="workspace_role_id" property="workspaceRoleId" jdbcType="VARCHAR"/>
        <result column="menu_id" property="menuId" jdbcType="INTEGER"/>
        <result column="create_user" property="createUser" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_user" property="updateUser" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="remarks" property="remarks" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
    </resultMap>
    <resultMap id="MenuBaseResultMap" type="com.cmpay.hacp.system.entity.MenuDO">
        <id column="menu_id" property="menuId" jdbcType="BIGINT"/>
        <result column="parent_id" property="parentId" jdbcType="BIGINT"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="app_id" property="appId" jdbcType="VARCHAR"/>
        <result column="url" property="url" jdbcType="VARCHAR"/>
        <result column="perms" property="perms" jdbcType="VARCHAR"/>
        <result column="type" property="type" jdbcType="VARCHAR"/>
        <result column="icon" property="icon" jdbcType="VARCHAR"/>
        <result column="order_num" property="orderNum" jdbcType="BIGINT"/>
        <result column="create_user_id" property="createUserId" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP"/>
        <result column="meta" property="meta" jdbcType="VARCHAR"/>
        <result column="component" property="component" jdbcType="VARCHAR"/>
        <result column="redirect" property="redirect" jdbcType="VARCHAR"/>
        <result column="en_name" property="enName" jdbcType="VARCHAR"/>
        <result column="parent_name" property="parentName" jdbcType="VARCHAR"/>
        <result column="hide_title" property="hideTitle" jdbcType="BOOLEAN"/>
        <result column="hidden" property="hidden" jdbcType="BOOLEAN"/>
        <result column="hide_children" property="hideChildren" jdbcType="BOOLEAN"/>
        <result column="keepalive" property="keepalive" jdbcType="BOOLEAN"/>
        <result column="hide_page_title_bar" property="hidePageTitleBar" jdbcType="BOOLEAN"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        ,
        workspace_role_id,
        menu_id,
        create_user,
        create_time,
        update_user,
        update_time,
        remarks,
        status
    </sql>
    <sql id="Menu_Base_Column_List">
        t1.menu_id,
            t1.parent_id,
            t1.name,
            t1.app_id,
            t1.url,
            t1.perms,
            t1.type,
            t1.icon,
            t1.order_num,
            t1.create_user_id,
            t1.create_time,
            t1.modify_time,
            t1.meta,
            t1.component,
            t1.redirect,
            t1.en_name,
            t1.parent_name,
            t1.hide_title,
            t1.hidden,
            t1.hide_children,
            t1.keepalive,
            t1.hide_page_title_bar
    </sql>
    <delete id="deleteMenuByWorkspaceRoleId" parameterType="java.lang.String">
        delete
        from tenant_workspace_role_menu
        where workspace_role_id = #{workspaceRoleId,jdbcType=VARCHAR}
    </delete>

    <select id="getMenuIdsByWorkspaceRoleId" resultType="java.lang.Integer">
        select menu_id
        from tenant_workspace_role_menu
        where workspace_role_id = #{workspaceRoleId,jdbcType=VARCHAR}
        order by menu_id asc
    </select>

    <select id="getWorkspaceMenusByUserId" parameterType="java.lang.String"
            resultMap="MenuBaseResultMap">
        SELECT
        <include refid="Menu_Base_Column_List"/>
        FROM sys_menu AS t1
        WHERE t1.menu_id IN (SELECT m.menu_id
        FROM sys_menu AS m,
        sys_role_menu AS r,
        sys_user_role AS u
        WHERE m.menu_id = r.menu_id
        AND r.role_id = u.role_id
        AND u.user_id = #{userId,jdbcType=VARCHAR}
        GROUP BY m.menu_id)
        UNION
        SELECT
        <include refid="Menu_Base_Column_List"/>
        FROM sys_menu AS t1
        WHERE t1.menu_id IN (SELECT menu_id
        FROM tenant_workspace_role_menu
        WHERE workspace_role_id IN (SELECT workspace_role_id
        FROM tenant_workspace_user_role
        WHERE workspace_role_id IN (SELECT workspace_role_id
        FROM tenant_workspace_role
        WHERE workspace_id = #{workspaceId,jdbcType=VARCHAR})
        AND user_id = #{userId,jdbcType=VARCHAR})
        GROUP BY menu_id)
        ORDER BY parent_id ASC,
        menu_id ASC,
        order_num ASC
    </select>
</mapper>
