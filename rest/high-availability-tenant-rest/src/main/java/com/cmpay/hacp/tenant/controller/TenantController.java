package com.cmpay.hacp.tenant.controller;

import com.cmpay.hacp.tenant.bo.TenantBO;
import com.cmpay.hacp.dto.tenant.*;
import com.cmpay.hacp.system.log.annotation.LogNoneRecord;
import com.cmpay.hacp.system.log.annotation.LogRecord;
import com.cmpay.hacp.tenant.service.TenantService;
import com.cmpay.hacp.utils.bean.BeanConvertUtil;
import com.cmpay.lemon.common.utils.BeanUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.framework.data.DefaultRspDTO;
import com.cmpay.lemon.framework.data.NoBody;
import com.cmpay.lemon.framework.page.PageInfo;
import com.cmpay.lemon.framework.security.SecurityUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.NotBlank;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/v1/tenant")
@Api(tags = "租户管理")
@Validated
public class TenantController {

    @Resource
    private TenantService tenantService;


    /**
     * 新增租户
     *
     * @param tenantAddDto 租户信息
     * @return 成功/失败
     */
    @PostMapping("/add")
    @ApiOperation(value = "新增租户", notes = "新增租户")
    @ApiResponse(code = 200, message = "返回结果")
    @LogRecord(title = "新增租户", action = "新增")
    @PreAuthorize("hasPermission('TenantController','tenant:admin:add')")
    public DefaultRspDTO<NoBody> addTenant(@Validated @RequestBody TenantAddDTO tenantAddDto) {
        TenantBO tenant = new TenantBO();
        BeanUtils.copyProperties(tenant, tenantAddDto);
        tenantService.addTenant(SecurityUtils.getLoginName(), tenant);
        return DefaultRspDTO.newSuccessInstance();
    }


    /**
     * 修改租户
     *
     * @param tenantUpdateDto 租户信息
     * @return 成功/失败
     */
    @PostMapping("/update")
    @ApiOperation(value = "修改租户", notes = "修改租户")
    @LogRecord(title = "修改租户", action = "修改")
    @ApiResponse(code = 200, message = "返回结果")
    @PreAuthorize("hasPermission('TenantController','tenant:admin:update')")
    public DefaultRspDTO<NoBody> updateTenant(@Validated @RequestBody TenantUpdateDTO tenantUpdateDto) {
        TenantBO tenant = new TenantBO();
        BeanUtils.copyProperties(tenant, tenantUpdateDto);
        tenantService.updateTenant(SecurityUtils.getLoginName(), tenant);
        return DefaultRspDTO.newSuccessInstance();
    }

    /**
     * 删除租户
     *
     * @param tenantDeleteDto 租户信息
     * @return 成功/失败
     */
    @PostMapping("/delete")
    @ApiOperation(value = "删除租户", notes = "删除租户")
    @LogRecord(title = "删除租户", action = "删除")
    @ApiResponse(code = 200, message = "返回结果")
    @PreAuthorize("hasPermission('TenantController','tenant:admin:delete')")
    public DefaultRspDTO<NoBody> deleteTenant(@Validated @RequestBody TenantDeleteDTO tenantDeleteDto) {
        tenantService.deleteTenant(SecurityUtils.getLoginName(), LocalDateTime.now(), tenantDeleteDto.getTenantId());
        return DefaultRspDTO.newSuccessInstance();
    }

    /**
     * 批量删除租户
     *
     * @param tenantDeletesDto 租户信息
     * @return 成功/失败
     */
    @PostMapping("/deletes")
    @ApiOperation(value = "批量删除租户", notes = "批量删除租户")
    @ApiResponse(code = 200, message = "返回结果")
    @LogRecord(title = "批量删除租户", action = "删除")
    @PreAuthorize("hasPermission('TenantController','tenant:admin:delete')")
    public DefaultRspDTO<NoBody> deleteTenants(@Validated @RequestBody TenantDeletesDTO tenantDeletesDto) {
        tenantService.deleteTenants(SecurityUtils.getLoginName(), LocalDateTime.now(), tenantDeletesDto.getTenantIds());
        return DefaultRspDTO.newSuccessInstance();
    }

    /**
     * 查询租户详情
     *
     * @param tenantId 租户信息
     * @return 租户详情
     */
    @GetMapping("/info")
    @ApiOperation(value = "查询租户详情", notes = "查询租户详情")
    @ApiResponse(code = 200, message = "租户详情")
    @LogRecord(title = "查询租户详情", action = "查询")
    @PreAuthorize("hasPermission('TenantController','tenant:admin:query')")
    public DefaultRspDTO<TenantDTO> getTenantInfo(
            @ApiParam(name = "tenantId", value = "租户ID", required = true, example = "0179035dc433428c84ff434379374157")
            @NotBlank(message = "HAC00004") @RequestParam("tenantId") String tenantId) {
        TenantBO tenant = tenantService.getDetailTenantInfo(tenantId);
        TenantDTO tenantDto = new TenantDTO();
        if (JudgeUtils.isNotNull(tenant)) {
            BeanUtils.copyProperties(tenantDto, tenant);
            if (JudgeUtils.isNotEmpty(tenant.getWorkspaces())) {
                tenantDto.setWorkspaces(BeanConvertUtil.convertList(tenant.getWorkspaces(), TenantWorkspaceDTO.class));
            }
        }
        return DefaultRspDTO.newSuccessInstance(tenantDto);
    }

    /**
     * 查询登录用户租户列表
     *
     * @return 登录用户租户列表
     */
    @GetMapping("/own/list")
    @ApiOperation(value = "查询登录用户租户列表", notes = "查询登录用户租户列表")
    @ApiResponse(code = 200, message = "登录用户租户列表")
    @LogRecord(title = "查询我的租户列表", action = "查询")
    public DefaultRspDTO<List<TenantDTO>> getOwnTenantList() {
        List<TenantBO> tenants = tenantService.getOwnTenantList(SecurityUtils.getLoginUserId());
        List<TenantDTO> hacpTenants = new ArrayList<>();
        if (JudgeUtils.isNotEmpty(tenants)) {
            hacpTenants = BeanConvertUtil.convertList(tenants, TenantDTO.class);
        }
        return DefaultRspDTO.newSuccessInstance(hacpTenants);
    }

    /**
     * 查询用户管理租户列表
     *
     * @return 用户管理租户列表
     */
    @GetMapping("/admin/list")
    @ApiOperation(value = "查询用户管理租户列表", notes = "查询用户管理租户列表")
    @ApiResponse(code = 200, message = "用户管理租户列表")
    @LogRecord(title = "查询管理租户列表", action = "查询")
    public DefaultRspDTO<List<TenantDTO>> getAdminTenantList() {
        List<TenantBO> tenants = tenantService.getAdminTenantList(SecurityUtils.getLoginUserId());
        List<TenantDTO> result = new ArrayList<>();
        if (JudgeUtils.isNotEmpty(tenants)) {
            result = BeanConvertUtil.convertList(tenants, TenantDTO.class);
        }
        return DefaultRspDTO.newSuccessInstance(result);
    }

    /**
     * 查询租户列表
     *
     * @param tenantName 租户名称
     * @return 租户列表
     */
    @GetMapping("/list")
    @ApiOperation(value = "查询租户列表", notes = "查询租户列表")
    @ApiResponse(code = 200, message = "租户列表")
    @LogRecord(title = "查询租户列表", action = "查询")
    public DefaultRspDTO<List<TenantDTO>> getTenantList(
            @ApiParam(name = "tenantName", value = "租户名称", required = false, example = "租户名称")
            @RequestParam(name = "tenantName", required = false) String tenantName) {
        TenantBO tenant = new TenantBO();
        tenant.setTenantName(tenantName);
        List<TenantBO> tenants = tenantService.getDetailTenants(tenant);
        List<TenantDTO> result = new ArrayList<>();
        if (JudgeUtils.isNotEmpty(tenants)) {
            result = BeanConvertUtil.convertList(tenants, TenantDTO.class);
        }
        return DefaultRspDTO.newSuccessInstance(result);
    }

    /**
     * 查询租户列表
     *
     * @param tenantName 租户名称
     * @return 租户列表
     */
    @GetMapping("/page")
    @ApiOperation(value = "分页查询租户列表", notes = "分页查询租户列表")
    @ApiResponse(code = 200, message = "分页租户列表")
    @LogNoneRecord
    @PreAuthorize("hasPermission('TenantController','tenant:admin:query')")
    public DefaultRspDTO<PageInfo<TenantDTO>> getTenantListByPage(@ApiParam(name = "tenantName",
            value = "租户名称",
            required = false,
            example = "租户名称")
    @RequestParam(name = "tenantName", required = false) String tenantName,
            @ApiParam(name = "pageNum", value = "第几页", required = false, defaultValue = "1", example = "1")
            @RequestParam(name = "pageNum", required = false, defaultValue = "1") Integer pageNum,
            @ApiParam(name = "pageSize", value = "一页多少条", required = false, defaultValue = "10", example = "10")
            @RequestParam(name = "pageSize", required = false, defaultValue = "10") Integer pageSize) {
        TenantBO tenant = new TenantBO();
        tenant.setTenantName(tenantName);
        PageInfo<TenantBO> tenantPage = tenantService.getTenantListByPage(pageNum, pageSize, tenant);
        PageInfo pageInfo = new PageInfo<>(new ArrayList<>());
        BeanUtils.copyProperties(pageInfo, tenantPage);
        if (JudgeUtils.isNotEmpty(tenantPage.getList())) {
            pageInfo.setList(BeanConvertUtil.convertList(tenantPage.getList(), TenantDTO.class));
        }
        return DefaultRspDTO.newSuccessInstance(pageInfo);
    }


}
