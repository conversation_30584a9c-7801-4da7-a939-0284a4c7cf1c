package com.cmpay.hacp.system.controller;

import com.cmpay.framework.data.response.GenericRspDTO;
import com.cmpay.hacp.api.SystemUserApi;
import com.cmpay.hacp.api.VersionApi;
import com.cmpay.hacp.system.bo.menu.PermMenuTreeMetaBO;
import com.cmpay.hacp.bo.system.RoleBO;
import com.cmpay.hacp.bo.system.UserBO;
import com.cmpay.hacp.system.bo.system.UserInfoBO;
import com.cmpay.hacp.dto.system.*;
import com.cmpay.hacp.system.log.annotation.LogNoneRecord;
import com.cmpay.hacp.system.log.annotation.LogRecord;
import com.cmpay.hacp.system.service.SystemPermissionService;
import com.cmpay.hacp.system.service.SystemUserService;
import com.cmpay.hacp.utils.bean.BeanConvertUtil;
import com.cmpay.lemon.common.utils.BeanUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.framework.annotation.QueryBody;
import com.cmpay.lemon.framework.data.NoBody;
import com.cmpay.lemon.framework.page.PageInfo;
import com.cmpay.lemon.framework.security.SecurityUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 用户
 *
 * <AUTHOR> tnw
 */
@Api(tags = "用户管理")
@RestController
@RequestMapping(VersionApi.VERSION_V1)
public class SystemUserController {

    @Autowired
    private SystemUserService systemUserService;
    @Autowired
    private SystemPermissionService systemPermissionService;

    @Value("${spring.application.name}")
    private String applicationName;

    /**
     * 查询用户信息
     *
     * @return
     */
    @ApiOperation("查询当前信息")
    @LogNoneRecord
    @GetMapping(SystemUserApi.GET_USER_INFO)
    public GenericRspDTO<UserInfoRspDTO> getUserInfo() {
        String userId = SecurityUtils.getLoginUserId();
        UserInfoRspDTO userInfo = this.getUserInfo(userId);
        PermMenuTreeMetaBO permMenuTreeMetaBO = systemPermissionService.queryUserPermissions(userId, applicationName);
        userInfo.setMenus(BeanConvertUtil.convertList(permMenuTreeMetaBO.getMenuTreeList(), MenuTreeRspMetaDTO.class));
        userInfo.setPermissions(systemPermissionService.getUserPermissions(userId, applicationName));
        return GenericRspDTO.newSuccessInstance(userInfo);
    }

    /**
     * 查询用户信息
     *
     * @param id
     * @return
     */
    @ApiOperation("查询指定用户信息")
    @LogRecord(title = "查询用户信息", action = "查询")
    @GetMapping(SystemUserApi.GET_USER_INFO_BY_ID)
    @PreAuthorize("hasPermission('SystemUserController','sys:user:query')")
    public GenericRspDTO<UserInfoRspDTO> getUserInfoById(@PathVariable("id") String id) {
        UserInfoRspDTO userInfo = this.getUserInfo(id);
        return GenericRspDTO.newSuccessInstance(userInfo);
    }


    /**
     * 分页查询用户列表
     *
     * @param userInfoQueryReqDTO
     * @return
     */
    @ApiOperation("分页查询")
    @LogNoneRecord
    @GetMapping(SystemUserApi.GET_USER_INFO_PAGE)
    @PreAuthorize("hasPermission('SystemUserController','sys:user:query')")
    public GenericRspDTO<UserInfoQueryRspDTO> getUsersByPage(@QueryBody UserInfoQueryReqDTO userInfoQueryReqDTO) {
        UserInfoBO userInfoBO = new UserInfoBO();
        BeanUtils.copyProperties(userInfoBO, userInfoQueryReqDTO);
        userInfoBO.setOperatorId(SecurityUtils.getLoginUserId());
        PageInfo<UserBO> pageInfo = systemUserService.getUsersByPage(userInfoQueryReqDTO.getPageSize(),
                userInfoQueryReqDTO.getPageNum(),
                userInfoBO);
        UserInfoQueryRspDTO userInfoQueryRspDTO = new UserInfoQueryRspDTO();
        if (JudgeUtils.isNotEmpty(pageInfo.getList())) {
            pageInfo.getList().forEach(userVO -> {
                userVO.setPassword(null);
            });
            userInfoQueryRspDTO.setList(pageInfo.getList());
        }
        userInfoQueryRspDTO.setPageNum(pageInfo.getPageNum());
        userInfoQueryRspDTO.setPageSize(pageInfo.getPageSize());
        userInfoQueryRspDTO.setPages(pageInfo.getPages());
        userInfoQueryRspDTO.setTotal(pageInfo.getTotal());
        return GenericRspDTO.newSuccessInstance(userInfoQueryRspDTO);
    }

    /**
     * 查询用户列表
     *
     * @return
     */
    @ApiOperation("用户列表")
    @LogNoneRecord
    @GetMapping(SystemUserApi.GET_USER_LIST)
    public GenericRspDTO<UserInfoQueryRspDTO> getAllUsers() {
        UserInfoQueryRspDTO userInfoQueryRspDTO = new UserInfoQueryRspDTO();
        List<UserBO> users = systemUserService.getAllUsers(SecurityUtils.getLoginUserId());
        if (JudgeUtils.isNotEmpty(users)) {
            users.forEach(userVO -> {
                userVO.setPassword(null);
            });
            userInfoQueryRspDTO.setList(users);
        }
        return GenericRspDTO.newSuccessInstance(userInfoQueryRspDTO);
    }


    /**
     * 用户新增
     *
     * @param userReqDTO
     * @return
     */
    @PostMapping(SystemUserApi.ADD)
    @LogRecord(title = "新增用户信息", action = "新增")
    @PreAuthorize("hasPermission('SystemUserController','sys:user:save')")
    public GenericRspDTO<NoBody> add(@RequestBody UserReqDTO userReqDTO) {
        UserBO userBO = userReqDTO.getUserInfo();
        if (JudgeUtils.isNotBlank(userBO.getPassword())) {
            //兼容带审核的流程
            userBO.setPassword(systemUserService.encryptPassword(userBO.getPassword()));
        }
        systemUserService.add(SecurityUtils.getLoginUserId(), userReqDTO.getUserInfo(), userReqDTO.getRoleIdList());
        return GenericRspDTO.newSuccessInstance();
    }

    /**
     * 用户修改
     *
     * @param userReqDTO
     * @return
     */
    @PostMapping(SystemUserApi.UPDATE)
    @LogRecord(title = "修改用户信息", action = "修改")
    @PreAuthorize("hasPermission('SystemUserController','sys:user:update')")
    public GenericRspDTO<NoBody> update(@RequestBody UserReqDTO userReqDTO) {
        UserBO userBO = userReqDTO.getUserInfo();
        if (JudgeUtils.isNotBlank(userBO.getPassword())) {
            userBO.setPassword(systemUserService.encryptPassword(userBO.getPassword()));
        }
        systemUserService.update(SecurityUtils.getLoginUserId(), userReqDTO.getUserInfo(), userReqDTO.getRoleIdList());
        return GenericRspDTO.newSuccessInstance();
    }


    /**
     * 删除用户
     *
     * @param deleteReqDTO
     * @return
     */
    @DeleteMapping(SystemUserApi.DELETE)
    @LogRecord(title = "删除用户信息", action = "删除")
    @PreAuthorize("hasPermission('SystemUserController','sys:user:delete')")
    public GenericRspDTO<NoBody> delete(@RequestBody UserDeleteReqDTO deleteReqDTO) {
        systemUserService.deleteBatch(deleteReqDTO.getUserIds());
        return GenericRspDTO.newSuccessInstance();
    }

    /**
     * 禁用用户
     *
     * @param deleteReqDTO
     * @return
     */
    @PostMapping(SystemUserApi.DISABLE)
    @LogRecord(title = "禁用用户", action = "修改")
    @PreAuthorize("hasPermission('SystemUserController','sys:user:disable')")
    public GenericRspDTO<NoBody> disable(@RequestBody UserDeleteReqDTO deleteReqDTO) {
        systemUserService.disableBatch(deleteReqDTO.getUserIds());
        return GenericRspDTO.newSuccessInstance();
    }

    /**
     * 启用用户
     *
     * @param deleteReqDTO
     * @return
     */
    @PostMapping(SystemUserApi.ENABLE)
    @LogRecord(title = "启用用户", action = "修改")
    @PreAuthorize("hasPermission('SystemUserController','sys:user:enable')")
    public GenericRspDTO<NoBody> enable(@RequestBody UserDeleteReqDTO deleteReqDTO) {
        systemUserService.enableBatch(deleteReqDTO.getUserIds());
        return GenericRspDTO.newSuccessInstance();
    }

    /**
     * 密码更新
     *
     * @param userPasswordReqDTO
     * @return
     */
    @PostMapping(SystemUserApi.UPDATE_PASSWORD)
    @LogRecord(title = "修改用户密码", action = "修改")
    @PreAuthorize("hasPermission('SystemUserController','sys:user:password')")
    public GenericRspDTO<NoBody> updatePassword(@RequestBody UserPasswordReqDTO userPasswordReqDTO) {
        systemUserService.updatePassword(SecurityUtils.getLoginUserId(),
                userPasswordReqDTO.getPassword(),
                userPasswordReqDTO.getNewPassword());
        return GenericRspDTO.newSuccessInstance();
    }

    /**
     * 查询用户信息
     *
     * @param userId
     * @return
     */
    private UserInfoRspDTO getUserInfo(String userId) {
        UserBO userBO = systemUserService.getUserInfo(userId);
        UserInfoRspDTO rspDTO = new UserInfoRspDTO();
        rspDTO.setUserInfo(userBO);
        if (JudgeUtils.isNotEmpty(userBO.getRoleList())) {
            rspDTO.setRoleIdList(userBO.getRoleList().stream().map(RoleBO::getRoleId).collect(Collectors.toList()));
        }
        return rspDTO;
    }

}
