package com.cmpay.hacp.inspection.controller;

import com.cmpay.hacp.inspection.application.service.TagService;
import com.cmpay.lemon.framework.data.DefaultRspDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 标签管理
 */
@RestController
@RequestMapping("/v1/inspection/tag")
@Tag(name = "标签管理")
@RequiredArgsConstructor
public class TagController {
    private final TagService tagService;

    /**
     * 创建标签
     *
     * @param tagName 标签名称
     * @return 创建结果
     */
    @PostMapping("/create")
    @Operation(summary = "创建标签", description = "创建标签")
    @ApiResponse(responseCode = "200", description = "成功")
    @PreAuthorize("hasPermission('TagController','inspection:tag:create')")
    public DefaultRspDTO<Long> createPlugin(@RequestBody String tagName) {

        Long tagId = tagService.createTag(tagName);

        return DefaultRspDTO.newSuccessInstance(tagId);
    }
}
