package com.cmpay.hacp.inspection.assembler;

import com.cmpay.hacp.inspection.domain.model.report.DailyInspectionReport;
import com.cmpay.hacp.inspection.domain.model.report.DailyInspectionReportDetail;
import com.cmpay.hacp.inspection.dto.DailyInspectionReportDetailRspDTO;
import com.cmpay.hacp.inspection.dto.DailyInspectionReportQueryReqDTO;
import com.cmpay.hacp.inspection.dto.DailyInspectionReportRspDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

/**
 * 按日巡检报告对象转换器
 */
@Mapper(componentModel = "spring")
public interface DailyInspectionReportDTOMapper {

    /**
     * 查询请求DTO转领域对象
     *
     * @param reqDTO 查询请求DTO
     * @return 领域对象
     */
    @Mapping(target = "ruleCount", ignore = true)
    @Mapping(target = "executionCount", ignore = true)
    @Mapping(target = "warningCount", ignore = true)
    @Mapping(target = "errorCount", ignore = true)
    @Mapping(target = "passRateChange", ignore = true)
    @Mapping(target = "averageResponseTime", ignore = true)
    DailyInspectionReport toDailyInspectionReport(DailyInspectionReportQueryReqDTO reqDTO);

    /**
     * 领域对象列表转响应DTO列表
     *
     * @param reportList 领域对象列表
     * @return 响应DTO列表
     */
    List<DailyInspectionReportRspDTO> toDailyInspectionReportRspDTOList(List<DailyInspectionReport> reportList);

    /**
     * 详细内容领域对象转响应DTO
     *
     * @param detail 详细内容领域对象
     * @return 详细内容响应DTO
     */
    DailyInspectionReportDetailRspDTO toDailyInspectionReportDetailRspDTO(DailyInspectionReportDetail detail);
}
