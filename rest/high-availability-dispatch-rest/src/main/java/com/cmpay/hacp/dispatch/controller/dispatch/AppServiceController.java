package com.cmpay.hacp.dispatch.controller.dispatch;

import com.cmpay.framework.data.response.GenericRspDTO;
import com.cmpay.hacp.dispatch.controller.dto.AppServicePageableReqDTO;
import com.cmpay.hacp.dispatch.controller.dto.AppServiceReqDTO;
import com.cmpay.hacp.dispatch.bo.AppServiceBO;
import com.cmpay.hacp.dispatch.service.AppServiceService;
import com.cmpay.hacp.system.log.annotation.LogNoneRecord;
import com.cmpay.hacp.system.log.annotation.LogRecord;
import com.cmpay.hacp.tenant.utils.TenantSecurityUtils;
import com.cmpay.hacp.tenant.utils.TenantUtils;
import com.cmpay.hacp.utils.bean.BeanConvertUtil;
import com.cmpay.lemon.framework.data.DefaultRspDTO;
import com.cmpay.lemon.framework.data.NoBody;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/v1/api-service")
@Api(tags = "业务服务管理")
public class AppServiceController {

    @Autowired
    private AppServiceService appServiceService;
    @ApiOperation(value = "add", notes = "添加业务服务", produces = "application/json")
    @ApiResponse(code = 200, message = "成功")
    @LogRecord(title = "新增业务服务", action = "新增")
    @PostMapping("/add")
    @PreAuthorize("hasPermission('AppServiceController','dispatch:api-service:add')")
    public DefaultRspDTO<NoBody> add(@RequestBody AppServiceReqDTO reqDTO) {
        TenantSecurityUtils.copyTenantSecurity(reqDTO);
        appServiceService.addAppService(reqDTO);
        return DefaultRspDTO.newSuccessInstance();
    }

    @ApiOperation(value = "page", notes = "获取业务服务分页列表", produces = "application/json")
    @ApiResponse(code = 200, message = "成功")
    @LogNoneRecord
    @GetMapping("/page")
    @PreAuthorize("hasPermission('AppServiceController','dispatch:api-service:query')")
    public GenericRspDTO<PageInfo<AppServiceBO>> getPage(AppServicePageableReqDTO reqDTO) {
        Optional.ofNullable(TenantUtils.getWorkspaceId()).ifPresent(workspaceId -> reqDTO.setWorkspaceId(workspaceId));
        PageInfo<AppServiceBO> pageInfo = appServiceService.getAppServicePage(reqDTO.getPageNum(), reqDTO.getPageSize(), BeanConvertUtil.convert(reqDTO, AppServiceBO.class));
        return GenericRspDTO.newSuccessInstance(pageInfo);
    }

    @ApiOperation(value = "list", notes = "获取业务服务列表", produces = "application/json")
    @ApiResponse(code = 200, message = "成功")
    @LogNoneRecord
    @GetMapping("/list")
    public GenericRspDTO<List<AppServiceBO>> getList() {
        List<AppServiceBO> pageInfo = appServiceService.getAppServiceList(TenantUtils.getWorkspaceId());
        return GenericRspDTO.newSuccessInstance(pageInfo);
    }

    @ApiOperation(value = "delete", notes = "删除业务服务", produces = "application/json")
    @ApiResponse(code = 200, message = "成功")
    @LogRecord(title = "删除业务服务", action = "删除")
    @PostMapping("/delete")
    @PreAuthorize("hasPermission('AppServiceController','dispatch:api-service:delete')")
    public DefaultRspDTO<NoBody> delete(@RequestBody AppServiceReqDTO reqDTO) {
        TenantSecurityUtils.copyTenantSecurity(reqDTO);
        appServiceService.deleteAppService(reqDTO);
        return DefaultRspDTO.newSuccessInstance();
    }

    @ApiOperation(value = "update", notes = "更新业务服务", produces = "application/json")
    @ApiResponse(code = 200, message = "成功")
    @LogRecord(title = "修改业务服务", action = "修改")
    @PostMapping("/update")
    @PreAuthorize("hasPermission('AppServiceController','dispatch:api-service:update')")
    public DefaultRspDTO<NoBody> update(@RequestBody AppServiceReqDTO reqDTO) {
        TenantSecurityUtils.copyTenantSecurity(reqDTO);
        appServiceService.updateAppService(reqDTO);
        return DefaultRspDTO.newSuccessInstance();
    }
}