package com.cmpay.hacp.dispatch.controller.dto;

import com.cmpay.hacp.bo.PageableReq;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @create 2024/05/15 11:22
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class DispatchZoneQueryDTO extends PageableReq {
    /**
     * @Fields zoneName 机房名称
     */
    @ApiModelProperty(value = "机房名称", required = true, example = "河西")
    private String zoneName;
    /**
     * @Fields zoneLabel 机房标识
     */
    @ApiModelProperty(value = "机房标识", required = true, example = "HX")
    private String zoneLabel;

    /**
     * @Fields remarks 备注
     */
    @ApiModelProperty(value = "备注", required = false, example = "租户备注")
    private String remarks;

}
