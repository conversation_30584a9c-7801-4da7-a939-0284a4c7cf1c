package com.cmpay.hacp.dispatch.controller.dispatch;

import com.cmpay.framework.data.response.GenericRspDTO;
import com.cmpay.hacp.dispatch.controller.dto.AppInstanceMachineReqDTO;
import com.cmpay.hacp.dispatch.bo.AppInstanceMachineBO;
import com.cmpay.hacp.dispatch.service.AppInstanceMachineService;
import com.cmpay.hacp.enums.MsgEnum;
import com.cmpay.hacp.system.log.annotation.LogNoneRecord;
import com.cmpay.hacp.system.log.annotation.LogRecord;
import com.cmpay.hacp.tenant.utils.TenantSecurityUtils;
import com.cmpay.lemon.framework.data.DefaultRspDTO;
import com.cmpay.lemon.framework.data.NoBody;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/v1/app-instance-machine")
@Api(tags = "业务节点管理-节点机器管理-3层级")
public class AppInstanceMachineController {

    @Autowired
    private AppInstanceMachineService appInstanceMachineService;
    @ApiOperation(value = "add", notes = "添加业务节点机器", produces = "application/json")
    @ApiResponse(code = 200, message = "成功")
    @LogRecord(title = "添加业务节点机器", action = "新增")
    @PostMapping("/add")
    @PreAuthorize("hasPermission('AppInstanceMachineController','dispatch:instance-machine:add')")
    public DefaultRspDTO<NoBody> add(@RequestBody AppInstanceMachineReqDTO reqDTO) {
        TenantSecurityUtils.copyTenantSecurity(reqDTO);
        appInstanceMachineService.addAppInstanceMachine(reqDTO);
        return DefaultRspDTO.newSuccessInstance();
    }

    @ApiOperation(value = "list", notes = "获取业务节机器列表", produces = "application/json")
    @ApiResponse(code = 200, message = "成功")
    @LogNoneRecord
    @GetMapping("/list")
    @PreAuthorize("hasPermission('AppInstanceMachineController','dispatch:instance-machine:query')")
    public GenericRspDTO<List<AppInstanceMachineBO>> getList(AppInstanceMachineReqDTO reqDTO) {
        TenantSecurityUtils.copyWorkspace(reqDTO);
        List<AppInstanceMachineBO> list = appInstanceMachineService.getAppInstanceMachineList(reqDTO);
        return GenericRspDTO.newInstance(MsgEnum.SUCCESS, list);
    }

    @ApiOperation(value = "delete", notes = "删除业务节点机器", produces = "application/json")
    @ApiResponse(code = 200, message = "成功")
    @LogRecord(title = "删除业务节点机器", action = "删除")
    @PostMapping("/delete")
    @PreAuthorize("hasPermission('AppInstanceMachineController','dispatch:instance-machine:delete')")
    public DefaultRspDTO<NoBody> delete(@RequestBody AppInstanceMachineReqDTO reqDTO) {
        TenantSecurityUtils.copyTenantSecurity(reqDTO);
        appInstanceMachineService.deleteAppInstanceMachine(reqDTO);
        return DefaultRspDTO.newSuccessInstance();
    }

    @ApiOperation(value = "update", notes = "更新业务节点机器", produces = "application/json")
    @ApiResponse(code = 200, message = "成功")
    @LogRecord(title = "修改业务节点机器", action = "修改")
    @PostMapping("/update")
    @PreAuthorize("hasPermission('AppInstanceMachineController','dispatch:instance-machine:update')")
    public DefaultRspDTO<NoBody> update(@RequestBody AppInstanceMachineReqDTO reqDTO) {
        TenantSecurityUtils.copyTenantSecurity(reqDTO);
        appInstanceMachineService.updateAppInstanceMachine(reqDTO);
        return DefaultRspDTO.newSuccessInstance();
    }
}