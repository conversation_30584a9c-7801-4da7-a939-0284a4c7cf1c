package com.cmpay.hacp.dispatch.controller.dto;

import com.cmpay.hacp.bo.PageableReq;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class DispatchPageableReqDTO extends PageableReq {

    private String workspaceId;

    private String apiLocationId;
    /**
     * @Fields apiTag 接口标签
     */
    private String apiTag;

    /**
     * @Fields isEmergency 0:常规调度  1:应急调度
     */
    private Byte isEmergency;

    private Byte status;

}
