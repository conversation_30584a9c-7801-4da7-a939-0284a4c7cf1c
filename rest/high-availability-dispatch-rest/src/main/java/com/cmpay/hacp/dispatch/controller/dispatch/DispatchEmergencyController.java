package com.cmpay.hacp.dispatch.controller.dispatch;

import com.cmpay.framework.data.response.GenericRspDTO;
import com.cmpay.hacp.dispatch.controller.dto.DispatchPageableReqDTO;
import com.cmpay.hacp.dispatch.controller.dto.DispatchReqDTO;
import com.cmpay.hacp.dispatch.bo.DispatchBO;
import com.cmpay.hacp.dispatch.service.DispatchService;
import com.cmpay.hacp.enums.ByteDispatchEnum;
import com.cmpay.hacp.enums.MsgEnum;
import com.cmpay.hacp.system.log.annotation.LogNoneRecord;
import com.cmpay.hacp.system.log.annotation.LogRecord;
import com.cmpay.hacp.tenant.utils.TenantSecurityUtils;
import com.cmpay.hacp.tenant.utils.TenantUtils;
import com.cmpay.hacp.utils.bean.BeanConvertUtil;
import com.cmpay.lemon.framework.data.DefaultRspDTO;
import com.cmpay.lemon.framework.data.NoBody;
import com.cmpay.lemon.framework.page.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Optional;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/v1/dispatch-emergency")
@Api(tags = "应急调度管理")
public class DispatchEmergencyController {

    @Autowired
    private DispatchService dispatchService;
    @ApiOperation(value = "添加调度", notes = "添加调度", produces = "application/json")
    @ApiResponse(code = 200, message = "成功")
    @PostMapping("/add")
    @LogRecord(title = "新增应急调度", action = "新增")
    @PreAuthorize("hasPermission('DispatchEmergencyController','dispatch:dispatch-emergency:add')")
    public DefaultRspDTO<NoBody> addDispatch(@RequestBody DispatchReqDTO reqDTO) {
        TenantSecurityUtils.copyTenantSecurity(reqDTO);
        reqDTO.setIsEmergency(ByteDispatchEnum.EMERGENCY.getValue());
        dispatchService.addDispatch(reqDTO);
        return DefaultRspDTO.newSuccessInstance();
    }

    @ApiOperation(value = "调度列表", notes = "调度列表 isEmergency 0:常规调度  1:应急调度", produces = "application/json")
    @ApiResponse(code = 200, message = "成功")
    @GetMapping("/list")
    @LogNoneRecord
    @PreAuthorize("hasPermission('DispatchEmergencyController','dispatch:dispatch-emergency:query')")
    public GenericRspDTO<PageInfo<DispatchBO>> getDispatchList(DispatchPageableReqDTO reqDTO) {
        Optional.ofNullable(TenantUtils.getWorkspaceId()).ifPresent(workspaceId -> reqDTO.setWorkspaceId(workspaceId));
        reqDTO.setIsEmergency(ByteDispatchEnum.EMERGENCY.getValue());
        PageInfo<DispatchBO> pageInfo = dispatchService.getDispatchList(reqDTO.getPageNum(), reqDTO.getPageSize(), BeanConvertUtil.convert(reqDTO, DispatchBO.class));
        return GenericRspDTO.newInstance(MsgEnum.SUCCESS, pageInfo);
    }

    @ApiOperation(value = "调度详情", notes = "调度详情", produces = "application/json")
    @ApiResponse(code = 200, message = "成功")
    @GetMapping("/{id}")
    @LogRecord(title = "查询应急调度详情", action = "查询")
    @PreAuthorize("hasPermission('DispatchEmergencyController','dispatch:dispatch-emergency:query')")
    public GenericRspDTO<DispatchBO> getInfo(@PathVariable("id") Integer dispatchId) {
        DispatchBO result = dispatchService.getInfo(dispatchId,TenantUtils.getWorkspaceId());
        return GenericRspDTO.newInstance(MsgEnum.SUCCESS, result);
    }

    @ApiOperation(value = "删除调度", notes = "删除调度", produces = "application/json")
    @ApiResponse(code = 200, message = "成功")
    @PostMapping("/delete")
    @LogRecord(title = "删除应急调度", action = "删除")
    @PreAuthorize("hasPermission('DispatchEmergencyController','dispatch:dispatch-emergency:delete')")
    public DefaultRspDTO<NoBody> deleteDispatchList(@RequestBody DispatchReqDTO reqDTO) {
        TenantSecurityUtils.copyTenantSecurity(reqDTO);
        reqDTO.setIsEmergency(ByteDispatchEnum.EMERGENCY.getValue());
        dispatchService.deleteDispatch(reqDTO);
        return DefaultRspDTO.newSuccessInstance();
    }


    @ApiOperation(value = "更新调度信息", notes = "更新调度信息", produces = "application/json")
    @ApiResponse(code = 200, message = "成功")
    @PostMapping("/update")
    @LogRecord(title = "修改应急调度", action = "修改")
    @PreAuthorize("hasPermission('DispatchEmergencyController','dispatch:dispatch-emergency:update')")
    public DefaultRspDTO<NoBody> updateDispatchList(@RequestBody DispatchReqDTO reqDTO) {
        TenantSecurityUtils.copyTenantSecurity(reqDTO);
        reqDTO.setIsEmergency(ByteDispatchEnum.EMERGENCY.getValue());
        dispatchService.updateDispatch(reqDTO);
        return DefaultRspDTO.newSuccessInstance();
    }

    @ApiOperation(value = "禁用", notes = "禁用", produces = "application/json")
    @ApiResponse(code = 200, message = "成功")
    @LogRecord(title = "禁用应急调度", action = "修改")
    @PostMapping("/disable/{id}")
    @PreAuthorize("hasPermission('DispatchEmergencyController','dispatch:dispatch-emergency:state')")
    public DefaultRspDTO<NoBody> disable(@PathVariable("id") Integer id) {
        DispatchBO capable = new DispatchBO();
        TenantSecurityUtils.copyTenantSecurity(capable);
        capable.setDispatchId(id);
        dispatchService.disable(capable);
        return DefaultRspDTO.newSuccessInstance();
    }

    @ApiOperation(value = "启用", notes = "启用", produces = "application/json")
    @ApiResponse(code = 200, message = "成功")
    @LogRecord(title = "启用应急调度", action = "修改")
    @PostMapping("/enable/{id}")
    @PreAuthorize("hasPermission('DispatchEmergencyController','dispatch:dispatch-emergency:state')")
    public DefaultRspDTO<NoBody> enable(@PathVariable("id") Integer id) {
        DispatchBO capable = new DispatchBO();
        TenantSecurityUtils.copyTenantSecurity(capable);
        capable.setDispatchId(id);
        dispatchService.enable(capable);
        return DefaultRspDTO.newSuccessInstance();
    }
}