package com.cmpay.hacp.dispatch.controller.dispatch;

import com.cmpay.framework.data.response.GenericRspDTO;
import com.cmpay.hacp.dispatch.controller.dto.RuleExpressionReqDTO;
import com.cmpay.hacp.dispatch.bo.RuleExpressionBO;
import com.cmpay.hacp.dispatch.service.RuleExpressionService;
import com.cmpay.hacp.enums.MsgEnum;
import com.cmpay.hacp.system.log.annotation.LogNoneRecord;
import com.cmpay.hacp.system.log.annotation.LogRecord;
import com.cmpay.hacp.tenant.utils.TenantSecurityUtils;
import com.cmpay.lemon.framework.data.DefaultRspDTO;
import com.cmpay.lemon.framework.data.NoBody;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/v1/rule-expression")
public class RuleExpressionController {

    @Autowired
    private RuleExpressionService ruleExpressionService;
    @ApiOperation(value = "add", notes = "添加规则表达式", produces = "application/json")
    @ApiResponse(code = 200, message = "成功")
    @LogRecord(title = "新增规则表达式", action = "新增")
    @PostMapping("/add")
    public DefaultRspDTO<NoBody> add(@RequestBody RuleExpressionReqDTO reqDTO) {
        TenantSecurityUtils.copyTenantSecurity(reqDTO);
        ruleExpressionService.addRuleExpression(reqDTO);
        return DefaultRspDTO.newSuccessInstance();
    }

    @ApiOperation(value = "list", notes = "获取规则表达式列表", produces = "application/json")
    @ApiResponse(code = 200, message = "成功")
    @LogNoneRecord
    @GetMapping("/list")
    public GenericRspDTO<List<RuleExpressionBO>> getList(RuleExpressionReqDTO reqDTO) {
        TenantSecurityUtils.copyWorkspace(reqDTO);
        List<RuleExpressionBO> list = ruleExpressionService.getRuleExpressionList(reqDTO);
        return GenericRspDTO.newInstance(MsgEnum.SUCCESS, list);
    }

    @ApiOperation(value = "delete", notes = "删除规则表达式", produces = "application/json")
    @ApiResponse(code = 200, message = "成功")
    @LogRecord(title = "删除规则表达式", action = "删除")
    @PostMapping("/delete")
    public DefaultRspDTO<NoBody> delete(@RequestBody RuleExpressionReqDTO reqDTO) {
        TenantSecurityUtils.copyTenantSecurity(reqDTO);
        ruleExpressionService.deleteRuleExpression(reqDTO);
        return DefaultRspDTO.newSuccessInstance();
    }

    @ApiOperation(value = "update", notes = "更新规则表达式", produces = "application/json")
    @ApiResponse(code = 200, message = "成功")
    @LogRecord(title = "修改规则表达式", action = "修改")
    @PostMapping("/update")
    public DefaultRspDTO<NoBody> update(@RequestBody RuleExpressionReqDTO reqDTO) {
        TenantSecurityUtils.copyTenantSecurity(reqDTO);
        ruleExpressionService.updateRuleExpression(reqDTO);
        return DefaultRspDTO.newSuccessInstance();
    }
}