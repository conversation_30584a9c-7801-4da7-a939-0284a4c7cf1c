package com.cmpay.hacp.dispatch.controller.dispatch;

import com.cmpay.hacp.dispatch.controller.dto.ApiLocationPageableReqDTO;
import com.cmpay.hacp.dispatch.controller.dto.ApiLocationReqDTO;
import com.cmpay.hacp.dispatch.bo.ApiLocationBO;
import com.cmpay.hacp.dispatch.service.ApiLocationService;
import com.cmpay.hacp.system.log.annotation.LogNoneRecord;
import com.cmpay.hacp.system.log.annotation.LogRecord;
import com.cmpay.hacp.tenant.utils.TenantSecurityUtils;
import com.cmpay.hacp.tenant.utils.TenantUtils;
import com.cmpay.hacp.utils.bean.BeanConvertUtil;
import com.cmpay.lemon.framework.data.DefaultRspDTO;
import com.cmpay.lemon.framework.data.NoBody;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/v1/api-location")
@Api(tags = "接口管理")
public class ApiLocationController {

    @Autowired
    private ApiLocationService apiLocationService;
    @ApiOperation(value = "add", notes = "添加API接口", produces = "application/json")
    @ApiResponse(code = 200, message = "成功")
    @PostMapping("/add")
    @LogRecord(title = "新增API接口", action = "新增")
    @PreAuthorize("hasPermission('ApiLocationController','dispatch:api:add')")
    public DefaultRspDTO<NoBody> add(@RequestBody ApiLocationReqDTO reqDTO) {
        TenantSecurityUtils.copyTenantSecurity(reqDTO);
        apiLocationService.addApiLocation(reqDTO);
        return DefaultRspDTO.newSuccessInstance();
    }

    @ApiOperation(value = "list", notes = "获取API接口分页列表", produces = "application/json")
    @ApiResponse(code = 200, message = "成功")
    @GetMapping("/page")
    @LogNoneRecord
    @PreAuthorize("hasPermission('ApiLocationController','dispatch:api:query')")
    public DefaultRspDTO<PageInfo<ApiLocationBO>> getPage(ApiLocationPageableReqDTO reqDTO) {
        Optional.ofNullable(TenantUtils.getWorkspaceId()).ifPresent(workspaceId -> reqDTO.setWorkspaceId(workspaceId));
        PageInfo<ApiLocationBO> pageInfo = apiLocationService.getApiLocationPage(reqDTO.getPageNum(), reqDTO.getPageSize(), BeanConvertUtil.convert(reqDTO, ApiLocationBO.class));
        return DefaultRspDTO.newSuccessInstance(pageInfo);
    }

    @ApiOperation(value = "list", notes = "获取API接口启用列表", produces = "application/json")
    @ApiResponse(code = 200, message = "成功")
    @LogNoneRecord
    @GetMapping("/list")
    public DefaultRspDTO<List<ApiLocationBO>> getList() {
        List<ApiLocationBO> pageInfo = apiLocationService.getApiLocationList(TenantUtils.getWorkspaceId());
        return DefaultRspDTO.newSuccessInstance(pageInfo);
    }

    @ApiOperation(value = "/{id}", notes = "获取API接口详情", produces = "application/json")
    @ApiResponse(code = 200, message = "成功")
    @GetMapping("/{id}")
    @LogRecord(title = "获取API接口详情", action = "查询")
    @PreAuthorize("hasPermission('ApiLocationController','dispatch:api:query')")
    public DefaultRspDTO<ApiLocationBO> getInfo(@PathVariable("id")Integer id) {
        ApiLocationBO pageInfo = apiLocationService.getInfo(id, TenantUtils.getWorkspaceId());
        return DefaultRspDTO.newSuccessInstance(pageInfo);
    }

    @ApiOperation(value = "delete", notes = "删除API接口", produces = "application/json")
    @ApiResponse(code = 200, message = "成功")
    @LogRecord(title = "删除API接口", action = "删除")
    @PostMapping("/delete")
    @PreAuthorize("hasPermission('ApiLocationController','dispatch:api:delete')")
    public DefaultRspDTO<NoBody> delete(@RequestBody ApiLocationReqDTO reqDTO) {
        TenantSecurityUtils.copyTenantSecurity(reqDTO);
        apiLocationService.deleteApiLocation(reqDTO);
        return DefaultRspDTO.newSuccessInstance();
    }

    @ApiOperation(value = "update", notes = "更新API接口", produces = "application/json")
    @ApiResponse(code = 200, message = "成功")
    @LogRecord(title = "修改API接口", action = "修改")
    @PostMapping("/update")
    @PreAuthorize("hasPermission('ApiLocationController','dispatch:api:update')")
    public DefaultRspDTO<NoBody> update(@RequestBody ApiLocationReqDTO reqDTO) {
        TenantSecurityUtils.copyTenantSecurity(reqDTO);
        apiLocationService.updateApiLocation(reqDTO);
        return DefaultRspDTO.newSuccessInstance();
    }
}