package com.cmpay.hacp.extend.sso.controller;


import com.cmpay.hacp.extend.sso.ticket.bo.ScimIamOrganization;
import com.cmpay.hacp.extend.sso.ticket.bo.ScimIamUser;
import com.cmpay.hacp.extend.sso.ticket.service.ScimIamService;
import com.cmpay.lemon.framework.data.DefaultRspDTO;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 新4A账户账户体系
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/v1/scim")
public class ScimIamController {

    @Resource
    private ScimIamService scimIamService;

    /**
     * 同步任务
     *
     * @return
     */
    @GetMapping("/sync")
    public DefaultRspDTO sync() {
        scimIamService.sync();
        return DefaultRspDTO.newSuccessInstance();
    }

    /**
     * 实时查询新4A用户
     *
     * @return
     */
    @GetMapping("/query/users")
    public DefaultRspDTO<List<ScimIamUser>> getAllScimIamUsers() {
        List<ScimIamUser> allScimIamPersons = scimIamService.getAllScimIamUsers();
        return DefaultRspDTO.newSuccessInstance(allScimIamPersons);
    }

    /**
     * 实时查询部分新4A用户
     *
     * @return
     */
    @GetMapping("/query/filter-users")
    public DefaultRspDTO<List<ScimIamUser>> getAllScimIamUsers(int startIndex, int count, String filter, String searchType) {
        List<ScimIamUser> allScimIamPersons = scimIamService.getAllScimIamUsers(startIndex, count, filter, searchType);
        return DefaultRspDTO.newSuccessInstance(allScimIamPersons);
    }


    /**
     * 全量同步新4A用户
     *
     * @return
     */
    @GetMapping("/sync/users")
    public DefaultRspDTO syncAllScimIamUsers() {
        scimIamService.syncAllScimIamUsers();
        return DefaultRspDTO.newSuccessInstance();
    }

    /**
     * 同步部分新4A用户
     *
     * @return
     */
    @GetMapping("/sync/filter-users")
    public DefaultRspDTO syncAllScimIamUsers(int startIndex, int count, String filter, String searchType) {
        scimIamService.syncAllScimIamUsers(startIndex, count, filter, searchType);
        return DefaultRspDTO.newSuccessInstance();
    }

    /**
     * 实时查询新4A组织架构
     *
     * @return
     */
    @GetMapping("/query/organizations")
    public DefaultRspDTO<List<ScimIamOrganization>> getAllScimIamOrganizations() {
        List<ScimIamOrganization> allScimIamOrganizations = scimIamService.getAllScimIamOrganizations();
        return DefaultRspDTO.newSuccessInstance(allScimIamOrganizations);
    }

    /**
     * 实时查询部分新4A组织架构
     *
     * @return
     */
    @GetMapping("/query/filter-organizations")
    public DefaultRspDTO<List<ScimIamOrganization>> getAllScimIamOrganizations(int startIndex, int count, String filter, String searchType) {
        List<ScimIamOrganization> allScimIamOrganizations = scimIamService.getAllScimIamOrganizations(startIndex, count, filter, searchType);
        return DefaultRspDTO.newSuccessInstance(allScimIamOrganizations);
    }

    /**
     * 全量同步新4A组织架构
     *
     * @return
     */
    @GetMapping("/sync/organizations")
    public DefaultRspDTO syncAllScimIamOrganizations() {
        scimIamService.syncAllScimIamOrganizations();
        return DefaultRspDTO.newSuccessInstance();
    }

    /**
     * 部分同步新4A组织架构
     *
     * @return
     */
    @GetMapping("/sync/filter-organizations")
    public DefaultRspDTO syncAllScimIamOrganizations(int startIndex, int count, String filter, String searchType) {
        scimIamService.syncAllScimIamOrganizations(startIndex, count, filter, searchType);
        return DefaultRspDTO.newSuccessInstance();
    }

}
