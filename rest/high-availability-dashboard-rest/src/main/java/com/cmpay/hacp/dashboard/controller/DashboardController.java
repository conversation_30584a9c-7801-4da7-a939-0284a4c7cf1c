package com.cmpay.hacp.dashboard.controller;

import com.cmpay.framework.data.response.GenericRspDTO;
import com.cmpay.hacp.dashboard.bo.PanelInfo;
import com.cmpay.hacp.dashboard.bo.PanelType;
import com.cmpay.hacp.dashboard.bo.Summary;
import com.cmpay.hacp.dashboard.service.PanelService;
import com.cmpay.hacp.dashboard.service.SummaryService;
import com.cmpay.hacp.dispatch.bo.DashboardDispatchBO;
import com.cmpay.hacp.dispatch.bo.DispatchConfigBO;
import com.cmpay.hacp.dispatch.service.DispatchConfigService;
import com.cmpay.hacp.dispatch.service.DispatchNodeService;
import com.cmpay.hacp.enums.MsgEnum;
import com.cmpay.hacp.system.log.annotation.LogNoneRecord;
import com.cmpay.hacp.tenant.utils.TenantUtils;
import com.cmpay.lemon.common.utils.StringUtils;
import com.cmpay.lemon.framework.data.DefaultRspDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/v1/dashboard")
@Api(tags = "首页管理")
public class DashboardController {
    private final SummaryService summaryService;
    private final PanelService panelService;

    private final DispatchNodeService dispatchNodeService;

    private final DispatchConfigService dispatchConfigService;

    public DashboardController(SummaryService summaryService,
            PanelService panelService,
            DispatchNodeService dispatchNodeService,
            DispatchConfigService dispatchConfigService) {
        this.summaryService = summaryService;
        this.panelService = panelService;
        this.dispatchNodeService = dispatchNodeService;
        this.dispatchConfigService = dispatchConfigService;
    }

    @ApiOperation(value = "summary", notes = "查询汇总信息", produces = "application/json")
    @ApiResponse(code = 200, message = "成功")
    @GetMapping("/summary")
    @LogNoneRecord
    public DefaultRspDTO<Summary> summary() {
        Summary summary = summaryService.getSummary(TenantUtils.getWorkspaceId());
        return DefaultRspDTO.newSuccessInstance(summary);
    }

    @ApiOperation(value = "query", notes = "查询面板信息", produces = "application/json")
    @ApiResponse(code = 200, message = "成功")
    @GetMapping("/panel/{type}")
    @LogNoneRecord
    public DefaultRspDTO<PanelInfo> queryPanel(@PathVariable("type") String type) {
        PanelType panelType = PanelType.valueOf(StringUtils.toRootUpperCase(type));
        PanelInfo info = panelService.queryPanel(TenantUtils.getWorkspaceId(), panelType);
        return DefaultRspDTO.newSuccessInstance(info);
    }

    @ApiOperation(value = "list", notes = "查询面板列表", produces = "application/json")
    @ApiResponse(code = 200, message = "成功")
    @GetMapping("/panels")
    @LogNoneRecord
    public DefaultRspDTO<List<PanelInfo>> listPanels() {
        List<PanelInfo> panelInfos = panelService.listPanels(TenantUtils.getWorkspaceId());
        return DefaultRspDTO.newSuccessInstance(panelInfos);
    }

    @ApiOperation(value = "list", notes = "首页机房统计列表", produces = "application/json")
    @ApiResponse(code = 200, message = "成功")
    @GetMapping("/zone-dispatch")
    @LogNoneRecord
    public DefaultRspDTO<List<DashboardDispatchBO>> getZoneDispatchList(){
        List<DashboardDispatchBO> list = dispatchNodeService.getZoneDispatchList(TenantUtils.getWorkspaceId());
        return DefaultRspDTO.newSuccessInstance(list);
    }



    @ApiOperation(value = "调度列表", notes = "调度列表 isEmergency 0:常规调度  1:应急调度", produces = "application/json")
    @ApiResponse(code = 200, message = "成功")
    @GetMapping("/drop-down")
    @LogNoneRecord
    public GenericRspDTO<DispatchConfigBO> getDispatchList() {
        DispatchConfigBO runningConfigBO =  dispatchConfigService.getPushDataDropDown(TenantUtils.getWorkspaceId());
        return GenericRspDTO.newInstance(MsgEnum.SUCCESS, runningConfigBO);
    }
}
