/*
 Navicat Premium Data Transfer

 Source Server         : ***********
 Source Server Type    : MySQL
 Source Server Version : 80033
 Source Host           : ***********:3306
 Source Schema         : dicp

 Target Server Type    : MySQL
 Target Server Version : 80033
 File Encoding         : 65001

 Date: 27/06/2024 10:23:29
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for import_history_file_info
-- ----------------------------
DROP TABLE IF EXISTS `import_history_file_info`;
CREATE TABLE `import_history_file_info`
(
	`id`                   int(11)                                                      NOT NULL AUTO_INCREMENT COMMENT '主键',
	`file_name`            varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci      NOT NULL COMMENT '文件名称',
	`file_blob`            mediumblob                                                   NULL COMMENT '文件内容',
	`excel_count`          int(11)                                                      NOT NULL DEFAULT 0 COMMENT 'API导入信息总计',
	`excel_success`        int(11)                                                      NOT NULL DEFAULT 0 COMMENT 'API导入信息成功数',
	`excel_fail`           int(11)                                                      NOT NULL DEFAULT 0 COMMENT 'API导入信息失败数',
	`excel_success_remark` varchar(2048) CHARACTER SET utf8 COLLATE utf8_general_ci     NULL     DEFAULT NULL COMMENT '成功序列（1,2,3）',
	`excel_fail_remark`    varchar(2048) CHARACTER SET utf8 COLLATE utf8_general_ci     NULL     DEFAULT NULL COMMENT '失败序列（4,5,6）',
	`batch_number`         varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci      NOT NULL COMMENT '批次号',
	`create_time`          datetime                                                     NOT NULL COMMENT '创建时间',
	`workspace_id`         varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '项目编号',
	`operator_id`          varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '操作人',
	`operator_name`        varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '操作名',
	PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 0
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_app
-- ----------------------------
DROP TABLE IF EXISTS `sys_app`;
CREATE TABLE `sys_app`
(
	`app_id`         varchar(32) CHARACTER SET utf8 COLLATE utf8_bin               NOT NULL COMMENT '应用系统编码',
	`app_name`       varchar(64) CHARACTER SET utf8 COLLATE utf8_bin               NOT NULL COMMENT '应用系统中文名称',
	`dept_id`        varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL COMMENT '部门id',
	`app_owner_id`   varchar(32) CHARACTER SET utf8 COLLATE utf8_bin               NOT NULL COMMENT '应用负责人',
	`app_key`        varchar(256) CHARACTER SET utf8 COLLATE utf8_bin              NULL DEFAULT NULL COMMENT '应用系统秘钥',
	`salt`           varchar(100) CHARACTER SET utf8 COLLATE utf8_bin              NULL DEFAULT NULL COMMENT '应用密钥加盐',
	`pub_key`        text CHARACTER SET utf8 COLLATE utf8_bin                      NULL COMMENT '应用公钥',
	`pri_key`        text CHARACTER SET utf8 COLLATE utf8_bin                      NULL COMMENT '应用私钥',
	`status`         varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NULL DEFAULT NULL COMMENT 'DISABLE:禁用  ENABLE:启用',
	`remark`         varchar(100) CHARACTER SET utf8 COLLATE utf8_bin              NULL DEFAULT NULL COMMENT '备注',
	`create_user_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci        NULL DEFAULT NULL COMMENT '创建者id',
	`create_time`    timestamp(6)                                                  NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '创建时间',
	`modify_time`    timestamp(6)                                                  NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6) COMMENT '更新时间',
	`token_key`      varchar(128) CHARACTER SET utf8 COLLATE utf8_bin              NULL DEFAULT '' COMMENT '访问token申请的key',
	`user_source`    varchar(32) CHARACTER SET utf8 COLLATE utf8_bin               NULL DEFAULT NULL COMMENT 'SELF_REGISTER 用户自己注册 ADMIN_REGISTER 系统注册',
	`sm_public_key`  varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'SM公钥',
	`sm_private_key` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'SM私钥',
	PRIMARY KEY (`app_id`) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci COMMENT = '应用系统注册表'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_dept
-- ----------------------------
DROP TABLE IF EXISTS `sys_dept`;
CREATE TABLE `sys_dept`
(
	`dept_id`        varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
	`parent_id`      varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '上级部门id，一级部门为0',
	`dept_name`      varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '部门名称',
	`order_num`      int(11)                                                NOT NULL COMMENT '排序',
	`status`         varchar(16) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'DISABLE:禁用  ENABLE:启用',
	`create_user_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建者id',
	`create_time`    timestamp(6)                                           NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '创建时间',
	`modify_time`    timestamp(6)                                           NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
	PRIMARY KEY (`dept_id`) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci COMMENT = '部门管理'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_dict
-- ----------------------------
DROP TABLE IF EXISTS `sys_dict`;
CREATE TABLE `sys_dict`
(
	`dict_id`     varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci   NOT NULL COMMENT '编号',
	`value`       varchar(2048) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '数据值',
	`label`       varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL COMMENT '标签名',
	`type`        varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL COMMENT '类型',
	`description` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL COMMENT '描述',
	`sort`        decimal(10, 0)                                                 NOT NULL COMMENT '排序（升序）',
	`parent_id`   varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci   NULL     DEFAULT '0' COMMENT '父级编号',
	`create_user` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci   NOT NULL COMMENT '创建者',
	`create_time` datetime                                                       NOT NULL COMMENT '创建时间',
	`update_user` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci   NOT NULL COMMENT '更新者',
	`update_time` datetime                                                       NOT NULL COMMENT '更新时间',
	`remarks`     varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NULL     DEFAULT NULL COMMENT '备注信息',
	`status`      varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci   NOT NULL DEFAULT '0' COMMENT '删除标记',
	PRIMARY KEY (`dict_id`) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci COMMENT = '字典表'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_dynamic_log
-- ----------------------------
DROP TABLE IF EXISTS `sys_dynamic_log`;
CREATE TABLE `sys_dynamic_log`
(
	`id`               varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL COMMENT 'ID',
	`request_id`       varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NULL DEFAULT NULL COMMENT '日志号',
	`operator`         varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NULL DEFAULT NULL COMMENT '操作者',
	`operation_action` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '操作动作',
	`execution_target` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '执行对象',
	`operator_ip`      varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '操作者IP地址',
	`operator_time`    datetime                                                      NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '执行时间',
	`operator_status`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NULL DEFAULT NULL COMMENT '操作状态',
	`data_size`        bigint(20)                                                    NULL DEFAULT NULL COMMENT '数据量级',
	`data_size_type`   varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NULL DEFAULT NULL COMMENT '数据量级单位(line-行数、byte-大小)',
	`application_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '应用名称/id',
	`data_it`          varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '数据所属对象',
	`data_path`        varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '流转路径',
	`params`           text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci         NULL COMMENT '请求参数',
	`interface_record` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '接口记录',
	`end_time`         datetime                                                      NULL DEFAULT NULL COMMENT '结束时间',
	`duration`         bigint(20)                                                    NULL DEFAULT NULL COMMENT '耗时(毫秒)',
	`type`             char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci      NULL DEFAULT '1' COMMENT '日志类型',
	`exception`        text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci         NULL COMMENT '异常信息',
	PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci COMMENT = '动态日志/调用第三方日志'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_log
-- ----------------------------
DROP TABLE IF EXISTS `sys_log`;
CREATE TABLE `sys_log`
(
	`id`                 varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL COMMENT '编号',
	`type`               char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci      NULL DEFAULT '1' COMMENT '日志类型',
	`title`              varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '操作动作',
	`user_id`            varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NULL DEFAULT NULL COMMENT '操作者ID',
	`user_name`          varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '操作者',
	`msg_cd`             varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NULL DEFAULT 'URM00000' COMMENT '消息码',
	`mobile`             varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NULL DEFAULT NULL COMMENT '操作者手机号码',
	`create_by`          varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NULL DEFAULT NULL COMMENT '操作者ID',
	`create_date`        datetime                                                      NULL DEFAULT NULL COMMENT '执行时间',
	`remote_addr`        varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '操作IP地址',
	`user_agent`         varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '用户终端',
	`request_uri`        varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '执行对象',
	`application_name`   varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '应用名称',
	`data_it`            varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '数据所属对象',
	`method`             varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NULL DEFAULT NULL COMMENT '操作方式',
	`params`             text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci         NULL COMMENT '请求参数',
	`exception`          text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci         NULL COMMENT '异常信息',
	`request_id`         varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NULL DEFAULT NULL COMMENT '日志号',
	`rsp_data_size`      bigint(20)                                                    NULL DEFAULT NULL COMMENT '响应报文大小',
	`rsp_data_size_type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NULL DEFAULT 'byte' COMMENT '响应报文大小类型(line-行数、byte-大小)',
	`duration`           bigint(20)                                                    NULL DEFAULT NULL COMMENT '耗时(毫秒-millisecond）',
	`end_time`           datetime                                                      NULL DEFAULT NULL COMMENT '结束时间',
	`tenant_id`          varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NULL DEFAULT NULL COMMENT '租户ID',
	`workspace_id`       varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NULL DEFAULT NULL COMMENT '项目ID',
	PRIMARY KEY (`id`) USING BTREE,
	INDEX `sys_log_pk_app_name` (`application_name` ASC) USING BTREE,
	INDEX `sys_log_pk_mobile` (`mobile` ASC) USING BTREE,
	INDEX `sys_log_pk_create_by` (`create_by` ASC) USING BTREE,
	INDEX `sys_log_pk_user_id` (`user_id` ASC) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci COMMENT = '日志表'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_login_history_log
-- ----------------------------
DROP TABLE IF EXISTS `sys_login_history_log`;
CREATE TABLE `sys_login_history_log`
(
	`id`             varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL COMMENT '主键id',
	`user_id`        varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL COMMENT '用户id',
	`user_name`      varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL     DEFAULT NULL COMMENT '用户名',
	`name`           varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NULL     DEFAULT NULL COMMENT '姓名',
	`mobile`         varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NULL     DEFAULT NULL COMMENT '手机号码',
	`login_ip`       varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL COMMENT '登录IP',
	`login_terminal` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '登录终端设备信息 user_agent',
	`login_from`     varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '登录来源',
	`login_date`     date                                                          NOT NULL COMMENT '登录日期',
	`login_time`     datetime                                                      NOT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '登录时间',
	`request_id`     varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL     DEFAULT NULL COMMENT '日志流水号',
	`is_use`         smallint(6)                                                   NOT NULL DEFAULT 1 COMMENT '是否可用(0-否,1-是)',
	PRIMARY KEY (`id`) USING BTREE,
	INDEX `sys_login_history_log_pk_user_name` (`user_name` ASC) USING BTREE,
	INDEX `sys_login_history_log_pk_mobile` (`mobile` ASC) USING BTREE,
	INDEX `sys_login_history_log_pk_name` (`name` ASC) USING BTREE,
	INDEX `sys_login_history_log_pk_user_id` (`user_id` ASC) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci COMMENT = '登录记录详情历史表'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_login_latest_info
-- ----------------------------
DROP TABLE IF EXISTS `sys_login_latest_info`;
CREATE TABLE `sys_login_latest_info`
(
	`id`          varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '主键',
	`pid`         varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '关联主键',
	`user_id`     varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '用户id',
	`latest_date` date                                                         NOT NULL COMMENT '最近一次登录日期',
	`latest_time` datetime                                                     NOT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '最近一次登录时间',
	`first_date`  date                                                         NOT NULL COMMENT '第一次登录日期',
	`first_time`  datetime                                                     NOT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '第一登录时间',
	`is_use`      smallint(6)                                                  NOT NULL DEFAULT 0 COMMENT '是否可用(0-否,1-是)',
	PRIMARY KEY (`id`) USING BTREE,
	UNIQUE INDEX `sys_login_latest_info_pk_user_id` (`user_id` ASC) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci COMMENT = '最近一次登录信息表'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_menu
-- ----------------------------
DROP TABLE IF EXISTS `sys_menu`;
CREATE TABLE `sys_menu`
(
	`menu_id`             bigint(20)                                       NOT NULL AUTO_INCREMENT COMMENT '菜单id',
	`parent_id`           bigint(20)                                       NOT NULL COMMENT '父菜单id，一级菜单为0',
	`name`                varchar(50) CHARACTER SET utf8 COLLATE utf8_bin  NOT NULL COMMENT '菜单名称',
	`app_id`              varchar(100) CHARACTER SET utf8 COLLATE utf8_bin NULL     DEFAULT NULL COMMENT '应用英文简称',
	`url`                 varchar(200) CHARACTER SET utf8 COLLATE utf8_bin NULL     DEFAULT NULL COMMENT '菜单url',
	`perms`               varchar(256) CHARACTER SET utf8 COLLATE utf8_bin NULL     DEFAULT NULL COMMENT '授权(多个用逗号分隔，如：user:list,user:create)',
	`type`                varchar(4) CHARACTER SET utf8 COLLATE utf8_bin   NULL     DEFAULT NULL COMMENT '类型   D：目录   M：菜单   B：按钮',
	`icon`                varchar(50) CHARACTER SET utf8 COLLATE utf8_bin  NULL     DEFAULT NULL COMMENT '菜单图标',
	`order_num`           bigint(20)                                       NULL     DEFAULT 0 COMMENT '排序',
	`create_user_id`      varchar(64) CHARACTER SET utf8 COLLATE utf8_bin  NULL     DEFAULT NULL COMMENT '创建者id',
	`create_time`         timestamp(6)                                     NULL     DEFAULT CURRENT_TIMESTAMP(6) COMMENT '创建时间',
	`modify_time`         timestamp(6)                                     NULL     DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6) COMMENT '修改时间',
	`meta`                varchar(256) CHARACTER SET utf8 COLLATE utf8_bin NULL     DEFAULT NULL COMMENT 'meta数据',
	`component`           varchar(64) CHARACTER SET utf8 COLLATE utf8_bin  NULL     DEFAULT NULL COMMENT '组件名',
	`redirect`            varchar(128) CHARACTER SET utf8 COLLATE utf8_bin NULL     DEFAULT NULL COMMENT '路由重定向跳转地址',
	`en_name`             varchar(32) CHARACTER SET utf8 COLLATE utf8_bin  NULL     DEFAULT NULL COMMENT '对应的英文名',
	`parent_name`         varchar(32) CHARACTER SET utf8 COLLATE utf8_bin  NULL     DEFAULT NULL COMMENT '父菜单名称',
	`hide_title`          tinyint(1)                                       NOT NULL DEFAULT 0 COMMENT '隐藏标题',
	`hidden`              tinyint(1)                                       NOT NULL DEFAULT 0 COMMENT '隐藏',
	`hide_children`       tinyint(1)                                       NOT NULL DEFAULT 0 COMMENT '隐藏子菜单',
	`keepalive`           tinyint(1)                                       NOT NULL DEFAULT 0 COMMENT '是否缓存',
	`hide_page_title_bar` tinyint(1)                                       NOT NULL DEFAULT 0 COMMENT '隐藏标题栏标题',
	PRIMARY KEY (`menu_id`) USING BTREE,
	INDEX `idx_appid` (`app_id` ASC) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 189177
  CHARACTER SET = utf8
  COLLATE = utf8_bin COMMENT = '菜单管理'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_role
-- ----------------------------
DROP TABLE IF EXISTS `sys_role`;
CREATE TABLE `sys_role`
(
	`role_id`        bigint(20)                                       NOT NULL AUTO_INCREMENT COMMENT '角色编号',
	`role_name`      varchar(100) CHARACTER SET utf8 COLLATE utf8_bin NULL     DEFAULT NULL COMMENT '角色名称',
	`remark`         varchar(100) CHARACTER SET utf8 COLLATE utf8_bin NULL     DEFAULT NULL COMMENT '备注',
	`status`         varchar(16) CHARACTER SET utf8 COLLATE utf8_bin  NOT NULL DEFAULT 'ENABLE' COMMENT 'DISABLE:禁用  ENABLE:启用',
	`dept_id`        varchar(64) CHARACTER SET utf8 COLLATE utf8_bin  NULL     DEFAULT NULL COMMENT '部门id',
	`create_user_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin  NULL     DEFAULT NULL COMMENT '创建者id',
	`create_time`    timestamp                                        NULL     DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
	`modify_time`    timestamp                                        NULL     DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
	`owner_app_id`   varchar(32) CHARACTER SET utf8 COLLATE utf8_bin  NULL     DEFAULT NULL COMMENT '归属应用',
	PRIMARY KEY (`role_id`) USING BTREE,
	UNIQUE INDEX `un_rolename_appid` (`role_name` ASC, `owner_app_id` ASC) USING BTREE,
	INDEX `idx_appid` (`owner_app_id` ASC) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 1108
  CHARACTER SET = utf8
  COLLATE = utf8_bin COMMENT = '角色'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_role_menu
-- ----------------------------
DROP TABLE IF EXISTS `sys_role_menu`;
CREATE TABLE `sys_role_menu`
(
	`id`      varchar(150) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
	`role_id` bigint(20)                                       NULL DEFAULT NULL COMMENT '角色id',
	`menu_id` bigint(20)                                       NULL DEFAULT NULL COMMENT '菜单id',
	PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8
  COLLATE = utf8_bin COMMENT = '用户与角色对应关系'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_user
-- ----------------------------
DROP TABLE IF EXISTS `sys_user`;
CREATE TABLE `sys_user`
(
	`user_id`         varchar(32) CHARACTER SET utf8 COLLATE utf8_bin  NOT NULL DEFAULT '' COMMENT '用户编号',
	`user_name`       varchar(128) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '用户名',
	`scim_user_id`    varchar(64) CHARACTER SET utf8 COLLATE utf8_bin  NULL     DEFAULT '' COMMENT '新4A用户编号',
	`scim_user_name`  varchar(128) CHARACTER SET utf8 COLLATE utf8_bin NULL     DEFAULT NULL COMMENT '新4A用户名',
	`full_name`       varchar(256) CHARACTER SET utf8 COLLATE utf8_bin NULL     DEFAULT NULL COMMENT '姓名',
	`password`        varchar(500) CHARACTER SET utf8 COLLATE utf8_bin NULL     DEFAULT NULL COMMENT '密码',
	`salt`            varchar(20) CHARACTER SET utf8 COLLATE utf8_bin  NULL     DEFAULT NULL COMMENT '盐',
	`dept_id`         varchar(64) CHARACTER SET utf8 COLLATE utf8_bin  NULL     DEFAULT NULL COMMENT '部门编号',
	`duty_id`         varchar(32) CHARACTER SET utf8 COLLATE utf8_bin  NULL     DEFAULT NULL COMMENT '岗位编号',
	`email`           varchar(256) CHARACTER SET utf8 COLLATE utf8_bin NULL     DEFAULT NULL COMMENT '邮箱',
	`mobile`          varchar(256) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '手机号',
	`weixin`          varchar(32) CHARACTER SET utf8 COLLATE utf8_bin  NULL     DEFAULT NULL COMMENT '微信号',
	`status`          varchar(16) CHARACTER SET utf8 COLLATE utf8_bin  NOT NULL DEFAULT 'ENABLE' COMMENT 'DISABLE:禁用  ENABLE:启用',
	`create_user_id`  varchar(64) CHARACTER SET utf8 COLLATE utf8_bin  NULL     DEFAULT NULL COMMENT '创建者id',
	`create_time`     timestamp(6)                                     NULL     DEFAULT CURRENT_TIMESTAMP(6) COMMENT '创建时间',
	`modify_time`     timestamp(6)                                     NULL     DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6) COMMENT '修改时间',
	`last_login_time` timestamp(6)                                     NULL     DEFAULT NULL COMMENT '用户上次登陆时间',
	`has_role`        varchar(20) CHARACTER SET utf8 COLLATE utf8_bin  NULL     DEFAULT NULL COMMENT '是否拥有角色 Y:是 N:否',
	`cst_user_id`     varchar(64) CHARACTER SET utf8 COLLATE utf8_bin  NULL     DEFAULT '' COMMENT '用户中心id',
	`app_id`          varchar(32) CHARACTER SET utf8 COLLATE utf8_bin  NULL     DEFAULT NULL COMMENT '应用id',
	`pwd_modify_time` timestamp(6)                                     NULL     DEFAULT NULL COMMENT '密码上次修改时间',
	PRIMARY KEY (`user_id`) USING BTREE,
	UNIQUE INDEX `un_username_appid` (`user_name` ASC, `app_id` ASC) USING BTREE,
	INDEX `idx_appid` (`app_id` ASC) USING BTREE,
	INDEX `uk_mobile_appid` (`mobile` ASC) USING BTREE,
	INDEX `scim_user_id_pk` (`scim_user_id` ASC) USING BTREE,
	INDEX `scim_user_name_index` (`scim_user_name` ASC) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8
  COLLATE = utf8_bin COMMENT = '系统用户'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_user_role
-- ----------------------------
DROP TABLE IF EXISTS `sys_user_role`;
CREATE TABLE `sys_user_role`
(
	`id`      varchar(150) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
	`user_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin  NULL DEFAULT NULL COMMENT '用户id',
	`role_id` bigint(20)                                       NULL DEFAULT NULL COMMENT '角色id',
	PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8
  COLLATE = utf8_bin COMMENT = '用户与角色对应关系'
  ROW_FORMAT = Dynamic;


-- ----------------------------
-- Records of sys_menu 菜单
-- ----------------------------
INSERT INTO `sys_menu` VALUES (1, 0, '系统管理', NULL, '/sys', 'sys:admin', 'D', 'SettingOutlined', 10, 'system', '2022-11-18 11:31:44.000000', '2024-07-30 14:59:46.000000', '{\"noCache\":false,\"hiddenTenantHeader\":true,\"name\":\"System Management\"}', 'routerView', '/sys/user', 'Sys', NULL, 0, 0, 0, 0, 0);
INSERT INTO `sys_menu` VALUES (4, 1, '用户管理', NULL, '/sys/user', 'admin:user', 'M', 'UserOutlined', 1, 'system', '2021-08-31 10:04:31.000000', '2024-07-01 10:42:28.350829', '{\"noCache\":false}', '/sys/user', NULL, 'User', NULL, 0, 0, 0, 0, 0);
INSERT INTO `sys_menu` VALUES (5, 1, '字典管理', NULL, '/sys/dict/index', 'admin:dict', 'M', 'CalendarOutlined', 5, 'system', '2021-08-31 10:04:31.000000', '2024-07-01 10:42:43.010237', '{\"noCache\":false}', '/sys/dict/index', NULL, 'Dict', NULL, 0, 0, 0, 0, 0);
INSERT INTO `sys_menu` VALUES (6, 1, '角色管理', NULL, '/sys/role', 'admin:role', 'M', 'TeamOutlined', 2, 'system', '2021-08-31 10:04:31.000000', '2024-07-01 10:42:53.847894', '{\"noCache\":false}', '/sys/role', NULL, 'Role', NULL, 0, 0, 0, 0, 0);
INSERT INTO `sys_menu` VALUES (7, 1, '菜单管理', NULL, '/sys/menu/index', 'admin:menu', 'M', 'ReadOutlined', 3, 'system', '2021-08-31 10:04:31.000000', '2024-07-01 10:43:05.285279', '{\"noCache\":false}', '/sys/menu/index', NULL, 'Menu', NULL, 0, 0, 0, 0, 0);
INSERT INTO `sys_menu` VALUES (11, 1, '部门管理', NULL, '/sys/department/index', 'admin:department', 'M', 'ClusterOutlined', 4, 'system', '2021-08-31 10:04:31.000000', '2024-07-01 10:43:13.336250', '{\"noCache\":false}', '/sys/department/index', NULL, 'Department', NULL, 0, 0, 0, 0, 0);
INSERT INTO `sys_menu` VALUES (15, 5, '修改', NULL, 'sys:dict:update', 'sys:dict:update', 'B', NULL, NULL, 'system', '2021-08-31 10:04:39.833498', '2021-08-31 10:05:13.386587', NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0);
INSERT INTO `sys_menu` VALUES (16, 5, '查询', NULL, 'sys:dict:query', 'sys:dict:query', 'B', NULL, NULL, 'system', '2021-08-31 10:04:39.000000', '2021-08-31 10:05:13.000000', '{}', NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0);
INSERT INTO `sys_menu` VALUES (17, 5, '新增', NULL, 'sys:dict:save', 'sys:dict:save', 'B', NULL, NULL, 'system', '2021-08-31 10:04:39.905473', '2021-08-31 10:05:13.433845', NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0);
INSERT INTO `sys_menu` VALUES (18, 5, '删除', NULL, 'sys:dict:delete', 'sys:dict:delete', 'B', NULL, NULL, 'system', '2021-08-31 10:04:39.984244', '2021-08-31 10:05:13.449308', NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0);
INSERT INTO `sys_menu` VALUES (25, 4, '新增', NULL, 'sys:user:save', 'sys:user:save', 'B', NULL, NULL, 'system', '2021-08-31 10:04:54.515301', '2021-08-31 10:05:13.682395', NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0);
INSERT INTO `sys_menu` VALUES (26, 4, '修改', NULL, 'sys:user:update', 'sys:user:update', 'B', NULL, NULL, 'system', '2021-08-31 10:04:54.530250', '2021-08-31 10:05:13.701060', NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0);
INSERT INTO `sys_menu` VALUES (27, 4, '删除', NULL, 'sys:user:delete', 'sys:user:delete', 'B', NULL, NULL, 'system', '2021-08-31 10:04:54.560608', '2021-08-31 10:05:13.742047', NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0);
INSERT INTO `sys_menu` VALUES (28, 4, '查询', NULL, 'sys:user:query', 'sys:user:query', 'B', NULL, NULL, 'system', '2021-08-31 10:04:54.000000', '2021-08-31 10:05:13.000000', '{}', NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0);
INSERT INTO `sys_menu` VALUES (29, 6, '新增', NULL, 'sys:role:save', 'sys:role:save', 'B', NULL, NULL, 'system', '2021-08-31 10:04:54.675703', '2021-08-31 10:05:13.772587', NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0);
INSERT INTO `sys_menu` VALUES (30, 6, '修改', NULL, 'sys:role:update', 'sys:role:update', 'B', NULL, NULL, 'system', '2021-08-31 10:04:54.689152', '2021-08-31 10:05:13.789096', NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0);
INSERT INTO `sys_menu` VALUES (31, 6, '删除', NULL, 'sys:role:delete', 'sys:role:delete', 'B', NULL, NULL, 'system', '2021-08-31 10:04:54.719283', '2021-08-31 10:05:13.807306', NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0);
INSERT INTO `sys_menu` VALUES (32, 6, '查询', NULL, 'sys:role:query', 'sys:role:query', 'B', NULL, NULL, 'system', '2021-08-31 10:04:54.000000', '2021-08-31 10:05:13.000000', '{}', NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0);
INSERT INTO `sys_menu` VALUES (33, 7, '新增', NULL, 'sys:menu:save', 'sys:menu:save', 'B', NULL, NULL, 'system', '2021-08-31 10:04:54.765430', '2021-08-31 10:05:13.856469', NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0);
INSERT INTO `sys_menu` VALUES (34, 7, '修改', NULL, 'sys:menu:update', 'sys:menu:update', 'B', NULL, NULL, 'system', '2021-08-31 10:04:54.787571', '2021-08-31 10:05:13.870167', NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0);
INSERT INTO `sys_menu` VALUES (35, 7, '删除', NULL, 'sys:menu:delete', 'sys:menu:delete', 'B', NULL, NULL, 'system', '2021-08-31 10:04:54.803162', '2021-08-31 10:05:13.905007', NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0);
INSERT INTO `sys_menu` VALUES (36, 7, '查询', NULL, 'sys:menu:query', 'sys:menu:query', 'B', NULL, NULL, 'system', '2021-08-31 10:04:54.000000', '2021-08-31 10:05:13.000000', '{}', NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0);
INSERT INTO `sys_menu` VALUES (44, 11, '新增', NULL, 'sys:department:save', 'sys:department:save', 'B', NULL, NULL, 'system', '2021-08-31 10:05:05.385504', '2021-08-31 10:05:14.294540', NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0);
INSERT INTO `sys_menu` VALUES (45, 11, '修改', NULL, 'sys:department:update', 'sys:department:update', 'B', NULL, NULL, 'system', '2021-08-31 10:05:05.411067', '2021-08-31 10:05:14.313532', NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0);
INSERT INTO `sys_menu` VALUES (46, 11, '删除', NULL, 'sys:department:delete', 'sys:department:delete', 'B', NULL, NULL, 'system', '2021-08-31 10:05:05.508171', '2021-08-31 10:05:14.345113', NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0);
INSERT INTO `sys_menu` VALUES (47, 11, '查询', NULL, 'sys:department:query', 'sys:department:query', 'B', NULL, NULL, 'system', '2021-08-31 10:05:05.000000', '2021-08-31 10:05:14.000000', '{}', NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0);
INSERT INTO `sys_menu` VALUES (59, 4, '导出', NULL, 'sys:user:export', 'sys:user:export', 'B', NULL, NULL, 'system', '2021-08-31 10:05:05.918276', '2021-08-31 10:05:14.740215', NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0);
INSERT INTO `sys_menu` VALUES (60, 4, '密码重置', NULL, 'sys:user:password', 'sys:user:password', 'B', NULL, NULL, 'system', '2021-08-31 10:05:05.941967', '2021-08-31 10:05:14.778970', NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0);
INSERT INTO `sys_menu` VALUES (61, 1, '字典参数', NULL, '/sys/dict/dict-data', 'admin:dict', 'M', 'CalendarOutlined', 6, 'system', '2021-08-31 10:04:31.000000', '2024-07-01 10:43:34.388536', '{\"noCache\":false,\"hidden\": true}', '/sys/dict/dict-data', NULL, 'DictData', NULL, 0, 1, 0, 0, 0);

-- ----------------------------
-- Records of sys_dict
-- ----------------------------
INSERT INTO `sys_dict` VALUES ('17', 'RSA', 'RSA秘钥', 'parent', '公共RSA公私钥', 17, '0', 'system', '2021-01-12 11:47:35', 'system', '2021-01-12 11:47:35', '系统字典项，SQL语句导入。', '0');
INSERT INTO `sys_dict` VALUES ('18', 'SM2', 'SM2秘钥', 'parent', 'SM2公共秘钥', 18, '0', 'system', '2021-01-12 13:59:11', 'system', '2021-01-12 13:59:11', '系统字典项，SQL语句导入。', '0');
INSERT INTO `sys_dict` VALUES ('19', 'SM4', 'SM4秘钥', 'parent', 'SM4秘钥', 19, '0', 'system', '2021-01-12 14:06:58', 'system', '2021-01-12 14:06:58', '系统字典项，SQL语句导入。', '0');
INSERT INTO `sys_dict` VALUES ('20', 'adminUser', '系统管理员', 'parent', '系统管理员', 20, '0', 'system', '2021-01-19 15:42:57', 'system', '2021-01-19 15:42:57', '系统字典项，SQL语句导入。', '0');
INSERT INTO `sys_dict` VALUES ('21', 'MENU_TYPE', '菜单类型', 'parent', '菜单类型(D-目录,M-菜单,B-按钮,L-链接)', 21, '0', 'system', '2021-08-25 16:02:53', 'system', '2021-08-25 16:02:53', '系统字典项，SQL语句导入。', '0');
INSERT INTO `sys_dict` VALUES ('45', 'LS0tLS1CRUdJTiBQVUJMSUMgS0VZLS0tLS0KTUlHZk1BMEdDU3FHU0liM0RRRUJBUVVBQTRHTkFEQ0JpUUtCZ1FDWWpNWkwzajY2MjBsRVljNFVHU2t4djY3VVUwLzZ2K0ZtU2p4YXBVZkhDL3AxcnJHNUo2bAp5dUswNlhUQXRCWldzL2dKUmk5ZzdJWlBmckd3dXJvYUd2a1M2clZuV3hHMnBVYm5pbU1SbjlZbU5NbWswUWNwRDJxSE02YmFGajBiWG5qYXdsSDVkMVNlUVZ4Vmg2dVAzSEZLClo3SkNVOW1HazBkRHZ6UmtDendJREFRQUIKLS0tLS1FTkQgUFVCTElDIEtFWS0tLS0t', 'publicKey', 'RSA', 'RSA公共公钥', 1, '17', 'system', '2021-01-12 11:53:29', 'system', '2021-01-12 11:53:29', '系统字典项，SQL语句导入。', '0');
INSERT INTO `sys_dict` VALUES ('46', 'TUlJQ2R3SUJBREFOQmdrcWhraUc5dzBCQVFFRkFBU0NBbUV3Z2dKZEFnRUFBb0dCQUppTXhrdmVQcnJiU1VSaHpoUVpLVEcvcnRSVFQvcS80V1pLUEZxbFI4Y0wrbld1c2JrbnFYSzRyVHBkTUMwRmxheitBbEdMMkRzaGs5K3NiQzZ1aG9hK1JMcXRXZGJFYmFsUnVlS1l4R2YxaVkweWFUUkJ5a1Bhb2N6cHRvV1BSdGVlTnJDVWZsM1ZKNUJYRldIcTQvY2NVcG5za0pUMllhVFIwTy9OR1FMUEFnTUJBQUVDZ1lBZ2lWWDU5SXI0bTdEQ3p3Ri9NK2FmWDd6UU9IekVoSXgwbnJ6RXNKL2dpbU4vR2lWNTBrWEgzQy9HVlAzamZGOXlLRE1pWld0WElWQkMwcW00UXkwWjE4Tlk0Y1BDSGxiQkpZbWE3RThrWGhRbnhuaWFPTU5lZHIxdnpyZGFXMDc4QUVxSzdaVXZxQ1dBM3NhUHpMS0Nqb2lwSHRMRzNWS3hGeEI5MjhwMlVRSkJBTXJTVkVDMWpzdkIzYnhCWGxvN2N2TU1VdGJIMVA2RDR5aWVBanFDZ0w2Z2dlSXJqMFJaUHphNDVFL1h5a1U4MXFnOVVOM3V3L0JNc05OaWJJb1VGT01DUVFEQWpDTnlPL2JEWENMNXJJVjBZTjlnVXJSU0xGUnVwckNDQ0Z3K3hJQWgrbWs4QStSUzBSdE1RSkw0RE1yQ0VqNWNOeU1MdENML1l3bmRxQW84QkdvbEFrQXZUYkRTeXNWOEprSUY1eVVQWEhBcDJZVDBqUFRUUzRWT2N0UElPNm0xVEJBZTVOUHNOL2h6VmRvQitTMDJ5c0t4eUdNMDc3d0pMb25MTTFobk45bkxBa0VBbkJBNkhyb0pqVmpkcEY2NElKYndGc09TRWxTSjd0RyttUGFyeGRoQlBza1hpS1JpNk5sQjlmRVU3bVozNVFzaWJsM21Vdk1qdHJvUVlnOTBsQ2JEM1FKQkFJTUxoNk51Y0tsckFnN3JpbjhmWDhLTjZ6alA4a1ZtMlBDQ3JBaFhnVkNmQllaM2ZPcURLdEcwamdneVRpaFdGMzNkV09vZmNWSHN4ZHhPR2VMNklyQT0=', 'privateKey', 'RSA', 'RSA公共私钥', 2, '17', 'system', '2021-01-12 11:56:25', 'system', '2021-01-12 11:56:25', '系统字典项，SQL语句导入。', '0');
INSERT INTO `sys_dict` VALUES ('47', '04cbd38273bc0c5f263c14557ce2c8101bf78e46ddacb83e87edab446939395127a96ec4042ca4c76c14b19c267a6cace8a208f06b7e141eb15faf7bbbb2921614', 'publicKey', 'SM2', 'SM2公共公钥', 2, '18', 'system', '2021-01-12 14:02:03', 'system', '2021-01-12 14:02:03', '系统字典项，SQL语句导入。', '0');
INSERT INTO `sys_dict` VALUES ('48', '00a6cbbfcb9908737e778b9ecd2bf3f2bb1c766b59878512cb830a388c0cb9f90e', 'privateKey', 'SM2', 'SM2公共私钥', 1, '18', 'system', '2021-01-12 14:02:16', 'system', '2021-01-12 14:02:16', '系统字典项，SQL语句导入。', '0');
INSERT INTO `sys_dict` VALUES ('49', '0123456789abcdeffedcba9876543210', 'key', 'SM4', 'SM4公共秘钥', 1, '19', 'system', '2021-01-15 15:45:50', 'system', '2021-01-15 15:45:50', '系统字典项，SQL语句导入。', '0');
INSERT INTO `sys_dict` VALUES ('4D44D52109894B9AB4D2FDB362C0A6DB', 'http://localhost', 'domain', 'hacp-system-management:localhost', '前端主页入口地址', 1, '0', '1', '2023-06-29 15:34:12', '1', '2023-06-29 15:34:12', '前端主页入口地址', '0');
INSERT INTO `sys_dict` VALUES ('51', '1', '系统管理员', 'adminUser', '系统管理员', 1, '20', 'system', '2021-01-19 15:46:22', 'system', '2021-01-19 15:46:22', '系统字典项，SQL语句导入。', '0');
INSERT INTO `sys_dict` VALUES ('53', '链接', 'L', 'MENU_TYPE', 'L-链接', 4, '21', 'system', '2021-08-25 16:06:59', 'system', '2021-08-25 16:06:59', '系统字典项，SQL语句导入。', '0');
INSERT INTO `sys_dict` VALUES ('54', '按钮', 'B', 'MENU_TYPE', 'B-按钮', 3, '21', 'system', '2021-08-25 16:07:09', 'system', '2021-08-25 16:07:09', '系统字典项，SQL语句导入。', '0');
INSERT INTO `sys_dict` VALUES ('55', '菜单', 'M', 'MENU_TYPE', 'M-菜单', 2, '21', 'system', '2021-08-25 16:07:20', 'system', '2021-08-25 16:07:20', '系统字典项，SQL语句导入。', '0');
INSERT INTO `sys_dict` VALUES ('56', '目录', 'D', 'MENU_TYPE', 'D-目录', 1, '21', 'system', '2021-08-25 16:07:30', 'system', '2021-08-25 16:07:30', '系统字典项，SQL语句导入。', '0');
INSERT INTO `sys_dict` VALUES ('CC051DEB7ECA4F7387FD624AF96300B6', 'http://www.it.test:32633/cas/p3/serviceValidate', 'ssoServerUrl:dev', 'ssoServer', 'sit环境租户门户单点登录认证地址', 1, '0', '1', '2023-06-29 14:43:37', '1', '2023-06-29 14:43:37', '系统字典项，SQL语句导入。', '0');
INSERT INTO `sys_dict` VALUES ('7A6DE6DA7A03477FB45BEC321DC7FCAA', 'HEAD', 'HEAD', 'requestMethod', 'HEAD', 4, '0EF6A974F00B48BF98D5DDD802F74541', '1', '2024-08-12 14:49:25', '1', '2024-08-12 14:49:25', 'HEAD', '0');
INSERT INTO `sys_dict` VALUES ('A60C6243BDD344A3B98A07F7AD21D7E2', 'PUT', 'PUT', 'requestMethod', 'PUT', 2, '0EF6A974F00B48BF98D5DDD802F74541', '1', '2024-08-12 14:49:09', '1', '2024-08-12 14:49:09', 'PUT', '0');
INSERT INTO `sys_dict` VALUES ('B0B1C3CB0A1347BA87A392757829639D', 'DELETE', 'DELETE', 'requestMethod', 'DELETE', 3, '0EF6A974F00B48BF98D5DDD802F74541', '1', '2024-08-12 14:49:17', '1', '2024-08-12 14:49:17', 'DELETE', '0');
INSERT INTO `sys_dict` VALUES ('CB43FCC265B8423A9DD7D11CEE1BAA0B', 'POST', 'POST', 'requestMethod', 'POST', 1, '0EF6A974F00B48BF98D5DDD802F74541', '1', '2024-08-12 14:49:01', '1', '2024-08-12 14:49:01', 'POST', '0');
INSERT INTO `sys_dict` VALUES ('CF849452F6EA4819BE8FBFD072F47EA1', 'CONNECT', 'CONNECT', 'requestMethod', 'CONNECT', 7, '0EF6A974F00B48BF98D5DDD802F74541', '1', '2024-08-12 14:49:46', '1', '2024-08-12 14:49:46', 'CONNECT', '0');
INSERT INTO `sys_dict` VALUES ('E382FF2CCF1A4F93B3660A1AB8D394D3', 'TRACE', 'TRACE', 'requestMethod', 'TRACE', 6, '0EF6A974F00B48BF98D5DDD802F74541', '1', '2024-08-12 14:49:39', '1', '2024-08-12 14:49:39', 'TRACE', '0');
INSERT INTO `sys_dict` VALUES ('FEAB8AFB20D24A6DA6DFCCA2BFAA654E', 'OPTIONS', 'OPTIONS', 'requestMethod', 'OPTIONS', 5, '0EF6A974F00B48BF98D5DDD802F74541', '1', '2024-08-12 14:49:32', '1', '2024-08-12 14:49:32', 'OPTIONS', '0');
INSERT INTO `sys_dict` VALUES ('0EF6A974F00B48BF98D5DDD802F74541', 'requestMethod', '请求方式', 'parent', '调度测试-新增/修改查看', 27, '0', '1', '2024-08-12 14:46:32', '1', '2024-08-12 14:46:32', '手动添加', '0');
INSERT INTO `sys_dict` VALUES ('FEE4C695397C466EA226CAC772EE9529', 'GET', 'GET', 'requestMethod', 'GET', 0, '0EF6A974F00B48BF98D5DDD802F74541', '1', '2024-08-12 14:48:53', '1', '2024-08-12 14:48:53', 'GET', '0');
INSERT INTO `sys_dict` VALUES ('100', 'taskType', '任务类型', 'parent', '对应预案原子任务类型，参数值请勿随意变更', 0, '0', '1', '2024-09-03 17:39:15', '1', '2024-09-03 17:39:15', '任务管理字典项，SQL语句导入。', '0');

-- ----------------------------
-- Records of sys_app
-- ----------------------------
INSERT INTO `sys_app` VALUES ('hacp-management', '高可用管控平台', '3', '1', '2c5a68a4310762bc67cf25d62de72d279dd407b903726ad3c2c97fa96c2dc813', 'Zi7W1Du823', '1f057a0c925a2d0d4898730a6bfee2883d77caea24b6cc1f5cdff8237ddb68a629f7c2ca160d95c5effb3ac6655c1314750322975eb4fab02aa0aa1d19e17e6db2802f103e446256a29ae9de8f13f2113d55ed434fd2e3db7a4f36cc53d05ae6cb38d45c47f29c7a4a15eb01d16b747b266df9983ac4c6a68476878686224fc9e53ee7363cb8f933cb5d7f59faa280b3', '3e5ac65ffff88fb2f223c7805f5c2a41cdd2b95beffed949126073d01ec90cc32d9b9159623190befa30774143351c0dca770f1fae83534ea2790b025e4ed14cdc78c230cb237be833f6916dafcd782e', 'ENABLE', '应用公钥', 'M0000000000001', '2020-03-03 09:18:28.000000', '2024-06-05 17:15:33.196282', 'c3c5f4e55da6b7eb65ca4158f3e5b3dda5fe4f758ab1c15ffaa3e782d8bfa74887c2a9f0a15142f9885cfa4ec4b1db64', 'ADMIN_REGISTER', '1f057a0c925a2d0d4898730a6bfee2883d77caea24b6cc1f5cdff8237ddb68a629f7c2ca160d95c5effb3ac6655c1314750322975eb4fab02aa0aa1d19e17e6db2802f103e446256a29ae9de8f13f2113d55ed434fd2e3db7a4f36cc53d05ae6cb38d45c47f29c7a4a15eb01d16b747b266df9983ac4c6a68476878686224fc9e53ee7363cb8f933cb5d7f59faa280b3', '3e5ac65ffff88fb2f223c7805f5c2a41cdd2b95beffed949126073d01ec90cc32d9b9159623190befa30774143351c0dca770f1fae83534ea2790b025e4ed14cdc78c230cb237be833f6916dafcd782e');


-- ----------------------------
-- Records of sys_user
-- ----------------------------
INSERT INTO `sys_user` VALUES ('1', 'admin', '', NULL, '8f3521e4ac2c9ea79f745d3d89f03e71', 'a816949f20f94b176ba46d7163ad165dbf294dfbf0006b447b84edad290dabbd', NULL, '1', '1', '1c16f38f4b1b16a8c8750405f4092efb0cd6cb170aac15e385e28a46c189b3f7', 'af19a652ca0bcd36376230be4e29bd8d', NULL, 'ENABLE', 'task', '2021-08-31 14:43:39.000000', '2024-07-05 10:40:59.791000', NULL, 'Y', '', NULL, NULL);

-- ----------------------------
-- Records of sys_role
-- ----------------------------
INSERT INTO `sys_role` VALUES (1, '系统管理员', '系统管理员', 'ENABLE', '1', 'system', '2021-08-31 10:03:49', '2021-08-31 10:04:21', NULL);
INSERT INTO `sys_role` VALUES (1076, '项目管理角色', '预制角色', 'ENABLE', '1', NULL, '2023-08-17 11:25:00', '2023-08-17 21:52:39', NULL);
INSERT INTO `sys_role` VALUES (1077, '项目普通角色', '预制角色', 'ENABLE', '1', NULL, '2023-08-17 11:26:26', '2023-08-17 21:34:47', NULL);

-- ----------------------------
-- Records of sys_role_menu
-- ----------------------------
INSERT INTO `sys_role_menu` VALUES ('1-1', 1, 1);
INSERT INTO `sys_role_menu` VALUES ('1-11', 1, 11);
INSERT INTO `sys_role_menu` VALUES ('1-15', 1, 15);
INSERT INTO `sys_role_menu` VALUES ('1-16', 1, 16);
INSERT INTO `sys_role_menu` VALUES ('1-17', 1, 17);
INSERT INTO `sys_role_menu` VALUES ('1-18', 1, 18);
INSERT INTO `sys_role_menu` VALUES ('1-189072', 1, 189072);
INSERT INTO `sys_role_menu` VALUES ('1-189073', 1, 189073);
INSERT INTO `sys_role_menu` VALUES ('1-25', 1, 25);
INSERT INTO `sys_role_menu` VALUES ('1-26', 1, 26);
INSERT INTO `sys_role_menu` VALUES ('1-27', 1, 27);
INSERT INTO `sys_role_menu` VALUES ('1-28', 1, 28);
INSERT INTO `sys_role_menu` VALUES ('1-29', 1, 29);
INSERT INTO `sys_role_menu` VALUES ('1-30', 1, 30);
INSERT INTO `sys_role_menu` VALUES ('1-31', 1, 31);
INSERT INTO `sys_role_menu` VALUES ('1-32', 1, 32);
INSERT INTO `sys_role_menu` VALUES ('1-33', 1, 33);
INSERT INTO `sys_role_menu` VALUES ('1-34', 1, 34);
INSERT INTO `sys_role_menu` VALUES ('1-35', 1, 35);
INSERT INTO `sys_role_menu` VALUES ('1-36', 1, 36);
INSERT INTO `sys_role_menu` VALUES ('1-4', 1, 4);
INSERT INTO `sys_role_menu` VALUES ('1-44', 1, 44);
INSERT INTO `sys_role_menu` VALUES ('1-45', 1, 45);
INSERT INTO `sys_role_menu` VALUES ('1-46', 1, 46);
INSERT INTO `sys_role_menu` VALUES ('1-47', 1, 47);
INSERT INTO `sys_role_menu` VALUES ('1-5', 1, 5);
INSERT INTO `sys_role_menu` VALUES ('1-59', 1, 59);
INSERT INTO `sys_role_menu` VALUES ('1-6', 1, 6);
INSERT INTO `sys_role_menu` VALUES ('1-60', 1, 60);
INSERT INTO `sys_role_menu` VALUES ('1-61', 1, 61);
INSERT INTO `sys_role_menu` VALUES ('1-7', 1, 7);

INSERT INTO `sys_role_menu` VALUES ('1076-1', 1076, 1);
INSERT INTO `sys_role_menu` VALUES ('1076-11', 1076, 11);
INSERT INTO `sys_role_menu` VALUES ('1076-15', 1076, 15);
INSERT INTO `sys_role_menu` VALUES ('1076-16', 1076, 16);
INSERT INTO `sys_role_menu` VALUES ('1076-17', 1076, 17);
INSERT INTO `sys_role_menu` VALUES ('1076-18', 1076, 18);
INSERT INTO `sys_role_menu` VALUES ('1076-189072', 1076, 189072);
INSERT INTO `sys_role_menu` VALUES ('1076-189073', 1076, 189073);
INSERT INTO `sys_role_menu` VALUES ('1076-25', 1076, 25);
INSERT INTO `sys_role_menu` VALUES ('1076-26', 1076, 26);
INSERT INTO `sys_role_menu` VALUES ('1076-27', 1076, 27);
INSERT INTO `sys_role_menu` VALUES ('1076-28', 1076, 28);
INSERT INTO `sys_role_menu` VALUES ('1076-29', 1076, 29);
INSERT INTO `sys_role_menu` VALUES ('1076-30', 1076, 30);
INSERT INTO `sys_role_menu` VALUES ('1076-31', 1076, 31);
INSERT INTO `sys_role_menu` VALUES ('1076-32', 1076, 32);
INSERT INTO `sys_role_menu` VALUES ('1076-33', 1076, 33);
INSERT INTO `sys_role_menu` VALUES ('1076-34', 1076, 34);
INSERT INTO `sys_role_menu` VALUES ('1076-35', 1076, 35);
INSERT INTO `sys_role_menu` VALUES ('1076-36', 1076, 36);
INSERT INTO `sys_role_menu` VALUES ('1076-4', 1076, 4);
INSERT INTO `sys_role_menu` VALUES ('1076-44', 1076, 44);
INSERT INTO `sys_role_menu` VALUES ('1076-45', 1076, 45);
INSERT INTO `sys_role_menu` VALUES ('1076-46', 1076, 46);
INSERT INTO `sys_role_menu` VALUES ('1076-47', 1076, 47);
INSERT INTO `sys_role_menu` VALUES ('1076-5', 1076, 5);
INSERT INTO `sys_role_menu` VALUES ('1076-59', 1076, 59);
INSERT INTO `sys_role_menu` VALUES ('1076-6', 1076, 6);
INSERT INTO `sys_role_menu` VALUES ('1076-60', 1076, 60);
INSERT INTO `sys_role_menu` VALUES ('1076-61', 1076, 61);
INSERT INTO `sys_role_menu` VALUES ('1076-7', 1076, 7);

-- ----------------------------
-- Records of sys_user_role
-- ----------------------------
INSERT INTO `sys_user_role` VALUES ('1-1', '1', 1);

-- ----------------------------
-- Records of sys_dept
-- ----------------------------
INSERT INTO `sys_dept` VALUES ('1', '0', '中国移动有限公司', 0, 'ENABLE', 'system', '2021-08-31 14:04:34.000000', '2024-07-31 15:58:59.110754');
INSERT INTO `sys_dept` VALUES ('10', '2', '商户运营事业部', 8, 'ENABLE', 'system', '2021-08-31 14:04:34.744242', '2021-08-31 14:05:02.700079');
INSERT INTO `sys_dept` VALUES ('11', '2', '个人金融事业部', 9, 'ENABLE', 'system', '2021-08-31 14:04:34.757750', '2021-08-31 14:05:04.562995');
INSERT INTO `sys_dept` VALUES ('12', '2', '技术创新中心', 10, 'ENABLE', 'system', '2021-08-31 14:04:34.773840', '2021-08-31 14:05:06.403260');
INSERT INTO `sys_dept` VALUES ('13', '2', '综合部', 11, 'ENABLE', 'system', '2021-08-31 14:04:34.824240', '2021-08-31 14:05:08.400448');
INSERT INTO `sys_dept` VALUES ('14', '2', '市场经营部', 12, 'ENABLE', 'system', '2021-08-31 14:04:34.880370', '2021-08-31 14:05:11.654416');
INSERT INTO `sys_dept` VALUES ('15', '2', '风险管理部', 13, 'ENABLE', 'system', '2021-08-31 14:04:34.904320', '2021-08-31 14:05:14.160811');
INSERT INTO `sys_dept` VALUES ('158', '2', '产品研发中心', 1, 'ENABLE', NULL, '2022-12-27 10:24:39.890548', '2023-10-13 10:02:12.153683');
INSERT INTO `sys_dept` VALUES ('159', '158', '测试', 4, 'ENABLE', NULL, '2022-12-27 11:57:07.738590', '2022-12-27 12:49:23.792890');
INSERT INTO `sys_dept` VALUES ('16', '2', '业务拓展中心', 15, 'ENABLE', 'system', '2021-08-31 14:04:34.920089', '2021-08-31 14:05:16.418627');
INSERT INTO `sys_dept` VALUES ('166', '2', '测试111', 11, 'ENABLE', NULL, '2022-12-27 21:51:03.722675', '2022-12-27 21:51:03.722675');
INSERT INTO `sys_dept` VALUES ('17', '2', '计划采购部', 14, 'ENABLE', 'system', '2021-08-31 14:04:34.934587', '2021-08-31 14:05:18.770532');
INSERT INTO `sys_dept` VALUES ('18', '2', '客户运营事业部', 16, 'ENABLE', 'system', '2021-08-31 14:04:34.952480', '2021-08-31 14:05:21.509212');
INSERT INTO `sys_dept` VALUES ('19', '5', '测试组', 17, 'ENABLE', 'system', '2021-08-31 14:04:34.975151', '2021-08-31 14:05:24.649343');
INSERT INTO `sys_dept` VALUES ('2', '1', '中移动金融科技公司', 1, 'ENABLE', 'system', '2021-08-31 14:04:34.593360', '2021-08-31 14:04:38.259112');
INSERT INTO `sys_dept` VALUES ('20', '5', '开发组', 18, 'ENABLE', 'system', '2021-08-31 14:04:34.999234', '2021-08-31 14:05:27.359113');
INSERT INTO `sys_dept` VALUES ('20E90C6878EA4A308C698C6B0179366B', NULL, '1', 1, 'ENABLE', NULL, '2024-06-17 10:30:51.948726', '2024-06-17 10:30:51.948726');
INSERT INTO `sys_dept` VALUES ('21', '5', '产品组', 2, 'ENABLE', 'system', '2021-08-31 14:04:35.015497', '2021-08-31 14:05:30.496500');
INSERT INTO `sys_dept` VALUES ('22', '5', '运维组', 1, 'ENABLE', 'system', '2021-08-31 14:04:35.045545', '2021-08-31 14:05:39.715056');
INSERT INTO `sys_dept` VALUES ('2751E924AE5E4A868050820EA58D7BF3', '6', 'sda', 213, 'ENABLE', NULL, '2024-07-24 17:16:58.800634', '2024-07-24 17:16:58.800634');
INSERT INTO `sys_dept` VALUES ('3', '2', '市场部', 1, 'ENABLE', 'system', '2021-08-31 14:04:34.627734', '2021-08-31 14:04:38.289270');
INSERT INTO `sys_dept` VALUES ('4', '2', '财务部', 2, 'ENABLE', 'system', '2021-08-31 14:04:34.645393', '2021-08-31 14:04:38.326551');
INSERT INTO `sys_dept` VALUES ('5', '2', '信息技术部', 3, 'ENABLE', 'system', '2021-08-31 14:04:34.663272', '2021-08-31 14:04:38.343390');
INSERT INTO `sys_dept` VALUES ('5a38bc9b64f84d33aad83f795b809e55', '159', '系统测试1', 2, 'ENABLE', NULL, '2023-10-08 10:03:35.675219', '2023-10-08 10:03:47.424021');
INSERT INTO `sys_dept` VALUES ('6', '2', '企业电商事业部', 4, 'ENABLE', 'system', '2021-08-31 14:04:34.682776', '2021-08-31 14:04:52.039997');
INSERT INTO `sys_dept` VALUES ('6BAAA7ABB0174D8B85C014B0481AC834', '6', 'ghf', 2312, 'ENABLE', NULL, '2024-07-24 17:19:40.454611', '2024-07-24 17:19:40.454611');
INSERT INTO `sys_dept` VALUES ('7', '2', '支付业务事业部', 5, 'ENABLE', 'system', '2021-08-31 14:04:34.695810', '2021-08-31 14:04:54.478446');
INSERT INTO `sys_dept` VALUES ('8', '2', '个人电商事业部', 6, 'ENABLE', 'system', '2021-08-31 14:04:34.715496', '2021-08-31 14:04:57.672798');
INSERT INTO `sys_dept` VALUES ('8BED99AA365746C7B36AF0716A0CACDA', NULL, '1', 1, 'ENABLE', NULL, '2024-06-17 10:31:40.878570', '2024-06-17 10:31:40.878570');
INSERT INTO `sys_dept` VALUES ('9', '2', '财务资金部', 7, 'ENABLE', 'system', '2021-08-31 14:04:34.727582', '2021-08-31 14:04:59.602551');
INSERT INTO `sys_dept` VALUES ('E2C3736D0C9B448C806889293BED015C', '6', 'sda', 123, 'ENABLE', NULL, '2024-07-24 17:16:46.427547', '2024-07-24 17:16:46.427547');

SET FOREIGN_KEY_CHECKS = 1;
