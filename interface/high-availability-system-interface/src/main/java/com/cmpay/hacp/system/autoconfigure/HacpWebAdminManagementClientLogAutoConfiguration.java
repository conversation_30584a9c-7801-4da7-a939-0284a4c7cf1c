package com.cmpay.hacp.system.autoconfigure;

import com.cmpay.hacp.system.client.SystemPermissionClient;
import com.cmpay.hacp.system.log.client.SystemDynamicLogClient;
import com.cmpay.hacp.system.log.service.SystemDynamicLogService;
import com.cmpay.hacp.system.log.service.SystemStaticLogService;
import com.cmpay.hacp.system.log.service.impl.SystemDynamicAccessLogClientImpl;
import com.cmpay.hacp.system.log.service.impl.SystemStaticLogClientImpl;
import com.cmpay.hacp.system.service.SystemPermissionService;
import com.cmpay.hacp.system.service.impl.SystemPermissionClientImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.AutoConfigureBefore;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Slf4j
@Configuration
@ConditionalOnProperty(prefix = "hacp.management.discovery", name = "enabled", havingValue = "true", matchIfMissing = true)
@AutoConfigureBefore({ HacpWebAdminLogAutoConfiguration.class})
public class HacpWebAdminManagementClientLogAutoConfiguration {


    @Bean
    @ConditionalOnClass({SystemStaticLogClientImpl.class})
    public SystemStaticLogService riseSystemLogClientImpl() {
        log.info("init RiseSystemLogClientImpl");
        return new SystemStaticLogClientImpl();
    }


    /**
     *
     * @param systemDynamicLogClient
     * @return
     */
    @Bean
    @ConditionalOnClass({SystemDynamicLogService.class, SystemDynamicLogClient.class, SystemDynamicAccessLogClientImpl.class})
    public SystemDynamicLogService dynamicAccessLogClientImpl(SystemDynamicLogClient systemDynamicLogClient) {
        log.info("init SystemDynamicLogClientImpl");
        return new SystemDynamicAccessLogClientImpl(systemDynamicLogClient);
    }

    /**
     *
     * @param systemPermissionClient
     * @return
     */
    @Bean
    @ConditionalOnClass({SystemPermissionClient.class, SystemDynamicAccessLogClientImpl.class})
    public SystemPermissionService systemPermissionClientImpl(SystemPermissionClient systemPermissionClient) {
        log.info("init SystemDynamicLogClientImpl");
        return new SystemPermissionClientImpl(systemPermissionClient);
    }
}
